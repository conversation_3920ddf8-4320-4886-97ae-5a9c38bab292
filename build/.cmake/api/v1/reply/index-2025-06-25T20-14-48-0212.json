{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/local/bin/cmake", "cpack": "/usr/local/bin/cpack", "ctest": "/usr/local/bin/ctest", "root": "/usr/local/share/cmake"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 2, "string": "4.0.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-ef3825ad2e72ed915d84.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-62dc11723a4b5b7d7348.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2941f388b21d3a3fe2b7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-8e553ef945920479955e.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-62dc11723a4b5b7d7348.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-ef3825ad2e72ed915d84.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-8e553ef945920479955e.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2941f388b21d3a3fe2b7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}