{"artifacts": [{"path": "WAAFer.app/Contents/MacOS/WAAFer"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_dependencies", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target", "include_directories"], "files": ["/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "/opt/homebrew/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6Config.cmake", "/opt/homebrew/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "/opt/homebrew/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "/usr/local/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/homebrew/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/opt/homebrew/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/opt/homebrew/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "/opt/homebrew/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:854:EVAL"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 110, "parent": 0}, {"command": 1, "file": 0, "line": 639, "parent": 1}, {"command": 0, "file": 0, "line": 688, "parent": 2}, {"command": 3, "file": 1, "line": 139, "parent": 0}, {"command": 4, "file": 1, "line": 113, "parent": 0}, {"command": 4, "file": 1, "line": 126, "parent": 0}, {"command": 7, "file": 1, "line": 25, "parent": 0}, {"file": 4, "parent": 7}, {"command": 7, "file": 4, "line": 212, "parent": 8}, {"file": 3, "parent": 9}, {"command": 6, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 5, "file": 2, "line": 62, "parent": 12}, {"command": 6, "file": 3, "line": 43, "parent": 10}, {"file": 9, "parent": 14}, {"command": 9, "file": 9, "line": 45, "parent": 15}, {"command": 8, "file": 8, "line": 137, "parent": 16}, {"command": 7, "file": 7, "line": 78, "parent": 17}, {"file": 6, "parent": 18}, {"command": 6, "file": 6, "line": 55, "parent": 19}, {"file": 5, "parent": 20}, {"command": 5, "file": 5, "line": 62, "parent": 21}, {"command": 7, "file": 4, "line": 212, "parent": 8}, {"file": 11, "parent": 23}, {"command": 6, "file": 11, "line": 55, "parent": 24}, {"file": 10, "parent": 25}, {"command": 5, "file": 10, "line": 62, "parent": 26}, {"command": 6, "file": 11, "line": 43, "parent": 24}, {"file": 14, "parent": 28}, {"command": 9, "file": 14, "line": 45, "parent": 29}, {"command": 8, "file": 8, "line": 137, "parent": 30}, {"command": 7, "file": 7, "line": 78, "parent": 31}, {"file": 13, "parent": 32}, {"command": 6, "file": 13, "line": 55, "parent": 33}, {"file": 12, "parent": 34}, {"command": 5, "file": 12, "line": 62, "parent": 35}, {"command": 4, "file": 0, "line": 640, "parent": 1}, {"command": 7, "file": 4, "line": 212, "parent": 8}, {"file": 16, "parent": 38}, {"command": 6, "file": 16, "line": 57, "parent": 39}, {"file": 15, "parent": 40}, {"command": 5, "file": 15, "line": 62, "parent": 41}, {"file": 1, "line": -1, "parent": 0}, {"command": 15, "file": 18, "line": 1, "parent": 43}, {"command": 14, "file": 0, "line": 816, "parent": 44}, {"command": 13, "file": 0, "line": 738, "parent": 45}, {"command": 12, "file": 0, "line": 738, "parent": 46}, {"command": 11, "file": 17, "line": 4344, "parent": 47}, {"command": 10, "file": 17, "line": 4119, "parent": 48}, {"command": 16, "file": 1, "line": 49, "parent": 0}, {"command": 16, "file": 1, "line": 50, "parent": 0}, {"command": 16, "file": 1, "line": 51, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0"}], "defines": [{"backtrace": 37, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 5, "define": "QT_OPENGL_LIB"}, {"backtrace": 5, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 5, "define": "QT_QMLMETA_LIB"}, {"backtrace": 5, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 5, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 5, "define": "QT_QML_LIB"}, {"backtrace": 5, "define": "QT_QUICK_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}], "frameworks": [{"backtrace": 37, "isSystem": true, "path": "/opt/homebrew/lib/QtCore.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtWidgets.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtGui.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQml.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtNetwork.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQuick.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQmlMeta.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQmlModels.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQmlWorkerScript.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtOpenGL.framework"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtMultimedia.framework"}], "includes": [{"backtrace": 0, "path": "/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include"}, {"backtrace": 50, "path": "/Volumes/Projects/_Code/_Waafer/src"}, {"backtrace": 51, "path": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include"}, {"backtrace": 52, "path": "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13"}, {"backtrace": 5, "path": "/Volumes/Projects/_Code/_Waafer/build/include"}, {"backtrace": 37, "isSystem": true, "path": "/opt/homebrew/lib/QtCore.framework/Headers"}, {"backtrace": 37, "isSystem": true, "path": "/opt/homebrew/share/qt/mkspecs/macx-clang"}, {"backtrace": 37, "isSystem": true, "path": "/opt/homebrew/include"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtWidgets.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtGui.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQml.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/include/QtQmlIntegration"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtNetwork.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQuick.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQmlMeta.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQmlModels.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtQmlWorkerScript.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtOpenGL.framework/Headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/homebrew/lib/QtMultimedia.framework/Headers"}], "language": "CXX", "languageStandard": {"backtraces": [37, 37], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 47]}], "dependencies": [{"id": "WAAFer_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "WAAFer_autogen::@6890427a1f51a3e7e1df"}, {"backtrace": 49, "id": "WAAFer_qmlimportscan::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "aaf-static::@bb53bcb17644034f00b0"}], "id": "WAAFer::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 4, "path": "."}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-F/opt/homebrew/lib", "role": "frameworkPath"}, {"fragment": "-Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/homebrew/lib/QtWidgets.framework/Versions/A/QtWidgets", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/homebrew/lib/QtQuick.framework/Versions/A/QtQuick", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/homebrew/lib/QtMultimedia.framework/Versions/A/QtMultimedia", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/lib/libpython3.13.dylib", "role": "libraries"}, {"backtrace": 5, "fragment": "lib/aaf", "role": "libraries"}, {"backtrace": 6, "fragment": "-framework Foundation", "role": "libraries"}, {"backtrace": 6, "fragment": "-framework CoreFoundation", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/homebrew/lib/QtQmlMeta.framework/Versions/A/QtQmlMeta", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/homebrew/lib/QtQmlWorkerScript.framework/Versions/A/QtQmlWorkerScript", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/homebrew/lib/QtQmlModels.framework/Versions/A/QtQmlModels", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/homebrew/lib/QtQml.framework/Versions/A/QtQml", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/homebrew/lib/QtOpenGL.framework/Versions/A/QtOpenGL", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/homebrew/lib/QtNetwork.framework/Versions/A/QtNetwork", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/homebrew/lib/QtGui.framework/Versions/A/QtGui", "role": "libraries"}, {"fragment": "-framework AGL", "role": "libraries"}, {"backtrace": 36, "fragment": "-framework AppKit", "role": "libraries"}, {"backtrace": 36, "fragment": "-framework OpenGL", "role": "libraries"}, {"backtrace": 36, "fragment": "-framework ImageIO", "role": "libraries"}, {"backtrace": 36, "fragment": "-framework Metal", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/homebrew/lib/QtCore.framework/Versions/A/QtCore", "role": "libraries"}, {"backtrace": 42, "fragment": "-framework IOKit", "role": "libraries"}, {"backtrace": 42, "fragment": "-framework DiskArbitration", "role": "libraries"}, {"backtrace": 42, "fragment": "-framework UniformTypeIdentifiers", "role": "libraries"}], "language": "CXX"}, "name": "WA<PERSON>er", "nameOnDisk": "WA<PERSON>er", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 47]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46]}, {"name": "", "sourceIndexes": [48, 49]}, {"name": "CMake Rules", "sourceIndexes": [50]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/WAAFer_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/AAFReader.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/LibAAFWrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/MemoryManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/ChunkedFileReader.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/ProgressTracker.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/AnalysisSettings.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/core/TrackOrganizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/python/PythonBridge.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/python/PythonInterpreter.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/audio/AudioAnalyzer.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/audio/AudioFileManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/audio/ClassificationEngine.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/audio/AudioPlaybackManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/audio/WebRTCVAD.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/ai/LMStudioClient.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/export/AAFExporter.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/ui/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/ui/TimelineWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/ui/ClassificationReviewDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/ui/AnalysisSettingsDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/utils/TimecodeUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "src/ui/PresetManagementDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "src/core/AAFReader.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/AAFTypes.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/LibAAFWrapper.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/MemoryManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/ChunkedFileReader.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/ProgressTracker.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/AnalysisSettings.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/core/TrackOrganizer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/python/PythonBridge.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/python/PythonInterpreter.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/audio/AudioAnalyzer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/audio/AudioFileManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/audio/ClassificationEngine.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/audio/AudioPlaybackManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/audio/WebRTCVAD.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/ai/LMStudioClient.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/export/AAFExporter.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/ui/MainWindow.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/ui/TimelineWidget.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/ui/ClassificationReviewDialog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/ui/AnalysisSettingsDialog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/utils/TimecodeUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "src/ui/PresetManagementDialog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "isGenerated": true, "path": "build/qrc_qml.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/WAAFer_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "path": "qml/qml.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/WAAFer_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}