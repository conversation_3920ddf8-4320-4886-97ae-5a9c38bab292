# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/Projects/_Code/_Waafer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/Projects/_Code/_Waafer/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/WAAFer.dir/all
all: third-party/LibAAF/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/WAAFer.dir/codegen
codegen: third-party/LibAAF/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: third-party/LibAAF/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/WAAFer.dir/clean
clean: CMakeFiles/WAAFer_qmlimportscan.dir/clean
clean: CMakeFiles/WAAFer_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/WAAFer_autogen.dir/clean
clean: third-party/LibAAF/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory third-party/LibAAF

# Recursive "all" directory target.
third-party/LibAAF/all: third-party/LibAAF/CMakeFiles/aaf-static.dir/all
.PHONY : third-party/LibAAF/all

# Recursive "codegen" directory target.
third-party/LibAAF/codegen: third-party/LibAAF/CMakeFiles/aaf-static.dir/codegen
.PHONY : third-party/LibAAF/codegen

# Recursive "preinstall" directory target.
third-party/LibAAF/preinstall:
.PHONY : third-party/LibAAF/preinstall

# Recursive "clean" directory target.
third-party/LibAAF/clean: third-party/LibAAF/CMakeFiles/aaf-static.dir/clean
third-party/LibAAF/clean: third-party/LibAAF/CMakeFiles/source_release.dir/clean
third-party/LibAAF/clean: third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/clean
third-party/LibAAF/clean: third-party/LibAAF/CMakeFiles/test.dir/clean
third-party/LibAAF/clean: third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/clean
third-party/LibAAF/clean: third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/clean
.PHONY : third-party/LibAAF/clean

#=============================================================================
# Target rules for target CMakeFiles/WAAFer.dir

# All Build rule for target.
CMakeFiles/WAAFer.dir/all: CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all
CMakeFiles/WAAFer.dir/all: CMakeFiles/WAAFer_autogen.dir/all
CMakeFiles/WAAFer.dir/all: CMakeFiles/WAAFer_qmlimportscan.dir/all
CMakeFiles/WAAFer.dir/all: third-party/LibAAF/CMakeFiles/aaf-static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer.dir/build.make CMakeFiles/WAAFer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer.dir/build.make CMakeFiles/WAAFer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27 "Built target WAAFer"
.PHONY : CMakeFiles/WAAFer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/WAAFer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/WAAFer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : CMakeFiles/WAAFer.dir/rule

# Convenience name for target.
WAAFer: CMakeFiles/WAAFer.dir/rule
.PHONY : WAAFer

# codegen rule for target.
CMakeFiles/WAAFer.dir/codegen: CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all
CMakeFiles/WAAFer.dir/codegen: CMakeFiles/WAAFer_qmlimportscan.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer.dir/build.make CMakeFiles/WAAFer.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27 "Finished codegen for target WAAFer"
.PHONY : CMakeFiles/WAAFer.dir/codegen

# clean rule for target.
CMakeFiles/WAAFer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer.dir/build.make CMakeFiles/WAAFer.dir/clean
.PHONY : CMakeFiles/WAAFer.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/WAAFer_qmlimportscan.dir

# All Build rule for target.
CMakeFiles/WAAFer_qmlimportscan.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_qmlimportscan.dir/build.make CMakeFiles/WAAFer_qmlimportscan.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_qmlimportscan.dir/build.make CMakeFiles/WAAFer_qmlimportscan.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=29 "Built target WAAFer_qmlimportscan"
.PHONY : CMakeFiles/WAAFer_qmlimportscan.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/WAAFer_qmlimportscan.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/WAAFer_qmlimportscan.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : CMakeFiles/WAAFer_qmlimportscan.dir/rule

# Convenience name for target.
WAAFer_qmlimportscan: CMakeFiles/WAAFer_qmlimportscan.dir/rule
.PHONY : WAAFer_qmlimportscan

# codegen rule for target.
CMakeFiles/WAAFer_qmlimportscan.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_qmlimportscan.dir/build.make CMakeFiles/WAAFer_qmlimportscan.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=29 "Finished codegen for target WAAFer_qmlimportscan"
.PHONY : CMakeFiles/WAAFer_qmlimportscan.dir/codegen

# clean rule for target.
CMakeFiles/WAAFer_qmlimportscan.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_qmlimportscan.dir/build.make CMakeFiles/WAAFer_qmlimportscan.dir/clean
.PHONY : CMakeFiles/WAAFer_qmlimportscan.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/WAAFer_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all: CMakeFiles/WAAFer_qmlimportscan.dir/all
CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all: third-party/LibAAF/CMakeFiles/aaf-static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen_timestamp_deps.dir/build.make CMakeFiles/WAAFer_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen_timestamp_deps.dir/build.make CMakeFiles/WAAFer_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Built target WAAFer_autogen_timestamp_deps"
.PHONY : CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/WAAFer_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 21
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : CMakeFiles/WAAFer_autogen_timestamp_deps.dir/rule

# Convenience name for target.
WAAFer_autogen_timestamp_deps: CMakeFiles/WAAFer_autogen_timestamp_deps.dir/rule
.PHONY : WAAFer_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/WAAFer_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen_timestamp_deps.dir/build.make CMakeFiles/WAAFer_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Finished codegen for target WAAFer_autogen_timestamp_deps"
.PHONY : CMakeFiles/WAAFer_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/WAAFer_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen_timestamp_deps.dir/build.make CMakeFiles/WAAFer_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/WAAFer_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/WAAFer_autogen.dir

# All Build rule for target.
CMakeFiles/WAAFer_autogen.dir/all: CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen.dir/build.make CMakeFiles/WAAFer_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen.dir/build.make CMakeFiles/WAAFer_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=28 "Built target WAAFer_autogen"
.PHONY : CMakeFiles/WAAFer_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/WAAFer_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/WAAFer_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : CMakeFiles/WAAFer_autogen.dir/rule

# Convenience name for target.
WAAFer_autogen: CMakeFiles/WAAFer_autogen.dir/rule
.PHONY : WAAFer_autogen

# codegen rule for target.
CMakeFiles/WAAFer_autogen.dir/codegen: CMakeFiles/WAAFer_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen.dir/build.make CMakeFiles/WAAFer_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=28 "Finished codegen for target WAAFer_autogen"
.PHONY : CMakeFiles/WAAFer_autogen.dir/codegen

# clean rule for target.
CMakeFiles/WAAFer_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/WAAFer_autogen.dir/build.make CMakeFiles/WAAFer_autogen.dir/clean
.PHONY : CMakeFiles/WAAFer_autogen.dir/clean

#=============================================================================
# Target rules for target third-party/LibAAF/CMakeFiles/aaf-static.dir

# All Build rule for target.
third-party/LibAAF/CMakeFiles/aaf-static.dir/all: third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all
third-party/LibAAF/CMakeFiles/aaf-static.dir/all: third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static.dir/depend
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48 "Built target aaf-static"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/all

# Build rule for subdir invocation for target.
third-party/LibAAF/CMakeFiles/aaf-static.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/LibAAF/CMakeFiles/aaf-static.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/rule

# Convenience name for target.
aaf-static: third-party/LibAAF/CMakeFiles/aaf-static.dir/rule
.PHONY : aaf-static

# codegen rule for target.
third-party/LibAAF/CMakeFiles/aaf-static.dir/codegen: third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48 "Finished codegen for target aaf-static"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/codegen

# clean rule for target.
third-party/LibAAF/CMakeFiles/aaf-static.dir/clean:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static.dir/clean
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/clean

#=============================================================================
# Target rules for target third-party/LibAAF/CMakeFiles/source_release.dir

# All Build rule for target.
third-party/LibAAF/CMakeFiles/source_release.dir/all: third-party/LibAAF/CMakeFiles/test.dir/all
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/source_release.dir/build.make third-party/LibAAF/CMakeFiles/source_release.dir/depend
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/source_release.dir/build.make third-party/LibAAF/CMakeFiles/source_release.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Built target source_release"
.PHONY : third-party/LibAAF/CMakeFiles/source_release.dir/all

# Build rule for subdir invocation for target.
third-party/LibAAF/CMakeFiles/source_release.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/LibAAF/CMakeFiles/source_release.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : third-party/LibAAF/CMakeFiles/source_release.dir/rule

# Convenience name for target.
source_release: third-party/LibAAF/CMakeFiles/source_release.dir/rule
.PHONY : source_release

# codegen rule for target.
third-party/LibAAF/CMakeFiles/source_release.dir/codegen: third-party/LibAAF/CMakeFiles/test.dir/all
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/source_release.dir/build.make third-party/LibAAF/CMakeFiles/source_release.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Finished codegen for target source_release"
.PHONY : third-party/LibAAF/CMakeFiles/source_release.dir/codegen

# clean rule for target.
third-party/LibAAF/CMakeFiles/source_release.dir/clean:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/source_release.dir/build.make third-party/LibAAF/CMakeFiles/source_release.dir/clean
.PHONY : third-party/LibAAF/CMakeFiles/source_release.dir/clean

#=============================================================================
# Target rules for target third-party/LibAAF/CMakeFiles/clean-cmake-files.dir

# All Build rule for target.
third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/all:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/build.make third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/depend
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/build.make third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Built target clean-cmake-files"
.PHONY : third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/all

# Build rule for subdir invocation for target.
third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/rule

# Convenience name for target.
clean-cmake-files: third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/rule
.PHONY : clean-cmake-files

# codegen rule for target.
third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/codegen:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/build.make third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Finished codegen for target clean-cmake-files"
.PHONY : third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/codegen

# clean rule for target.
third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/clean:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/build.make third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/clean
.PHONY : third-party/LibAAF/CMakeFiles/clean-cmake-files.dir/clean

#=============================================================================
# Target rules for target third-party/LibAAF/CMakeFiles/test.dir

# All Build rule for target.
third-party/LibAAF/CMakeFiles/test.dir/all:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/test.dir/build.make third-party/LibAAF/CMakeFiles/test.dir/depend
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/test.dir/build.make third-party/LibAAF/CMakeFiles/test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Built target test"
.PHONY : third-party/LibAAF/CMakeFiles/test.dir/all

# Build rule for subdir invocation for target.
third-party/LibAAF/CMakeFiles/test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/LibAAF/CMakeFiles/test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : third-party/LibAAF/CMakeFiles/test.dir/rule

# Convenience name for target.
test: third-party/LibAAF/CMakeFiles/test.dir/rule
.PHONY : test

# codegen rule for target.
third-party/LibAAF/CMakeFiles/test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/test.dir/build.make third-party/LibAAF/CMakeFiles/test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Finished codegen for target test"
.PHONY : third-party/LibAAF/CMakeFiles/test.dir/codegen

# clean rule for target.
third-party/LibAAF/CMakeFiles/test.dir/clean:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/test.dir/build.make third-party/LibAAF/CMakeFiles/test.dir/clean
.PHONY : third-party/LibAAF/CMakeFiles/test.dir/clean

#=============================================================================
# Target rules for target third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir

# All Build rule for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Built target aaf-static_autogen_timestamp_deps"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/rule

# Convenience name for target.
aaf-static_autogen_timestamp_deps: third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/rule
.PHONY : aaf-static_autogen_timestamp_deps

# codegen rule for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num= "Finished codegen for target aaf-static_autogen_timestamp_deps"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/codegen

# clean rule for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/clean
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir

# All Build rule for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/all: third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=49 "Built target aaf-static_autogen"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/all

# Build rule for subdir invocation for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Projects/_Code/_Waafer/build/CMakeFiles 0
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/rule

# Convenience name for target.
aaf-static_autogen: third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/rule
.PHONY : aaf-static_autogen

# codegen rule for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/codegen: third-party/LibAAF/CMakeFiles/aaf-static_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=49 "Finished codegen for target aaf-static_autogen"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/codegen

# clean rule for target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build.make third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/clean
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

