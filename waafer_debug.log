=== WAAFer Application Starting ===
=== WAAFer AI-Powered AAF Audio Organizer ===
Qt Version: 6.9.0
Platform: "macOS Sequoia (15.5)"
Creating QApplication...
QApplication created successfully
Starting WAAFer AI-powered AAF Audio Organizer
Initializing Python bridge...
Creating PythonBridge...
PythonInterpreter created successfully
Attempting to initialize Python bridge...
Initializing Python bridge...
Initializing Python interpreter...
Searching for Python executable...
Virtual environment not found, trying system Python...
Found system Python executable at: "/usr/bin/python3"
Using Python executable: "/usr/bin/python3"
Testing Python with simple script...
Starting Python process with executable: "/usr/bin/python3"
Python script: "print('Python OK')"
Python interpreter initialized successfully
Python bridge initialized successfully
Python bridge initialized successfully
Testing Python connection...
Executing Python command: "result = 2 + 2; print(f'Test result: {result}')"
Starting Python process with executable: "/usr/bin/python3"
Python script: "result = 2 + 2; print(f'Test result: {result}')"
Python connection test successful
Python bridge test passed
Initializing LibAAF wrapper...
LibAAFWrapper: Initializing LibAAF...
LibAAFWrapper: Successfully initialized
LibAAF wrapper initialized successfully
Initializing core components...
MemoryManager initialized with 16 GB limit
MemoryManager initialized
AAFReader initialized
AAFReader initialized
Initializing Phase 2 audio components...
AudioAnalyzer initialized
AudioAnalyzer initialized
Audio cache directory: "/Users/<USER>/Library/Caches/WAAFer Audio Organizer/WAAFer/WAAFer/audio"
AudioFileManager initialized
AudioFileManager initialized
Initializing Phase 3 components...
ProgressTracker initialized
ProgressTracker initialized
Built-in analysis presets initialized
Analysis settings loaded
AnalysisSettings initialized
AnalysisSettings initialized
ClassificationEngine: Constructor started
ClassificationEngine: Initialized with 12 threads for parallel processing
ClassificationEngine: Thread pool max thread count: 12
ClassificationEngine: Constructor completed
ClassificationEngine initialized
TrackOrganizer initialized with 4 templates
TrackOrganizer initialized
Initializing Phase 4 components...
LMStudioClient initialized with server URL: "http://*************:5001"
LMStudioClient initialized
Initializing AudioPlaybackManager...
AudioPlaybackManager: Constructor started
AudioPlaybackManager: Skipping audio component initialization to prevent hang
AudioPlaybackManager: Setting up timers
AudioPlaybackManager: Constructor completed (audio disabled)
AudioPlaybackManager initialized
Initializing AAF Exporter...
AAFExporter initialized
AAFExporter initialized
All core components initialized successfully
=== CREATING MAIN WINDOW ===
=== MainWindow: Loading settings ===
loadSettings: Starting settings load
loadSettings: QSettings object created
loadSettings: Loading LLM settings
loadSettings: LM Studio URL loaded: "http://*************:5001"
loadSettings: OpenAI API key loaded
loadSettings: Anthropic API key loaded
loadSettings: Use local LLM setting loaded: true
loadSettings: Loading analysis settings
loadSettings: Quality setting loaded: "Fast"
loadSettings: Chunk skip factor loaded: 8
loadSettings: Max concurrent loaded: 1
loadSettings: Fast mode loaded: true
loadSettings: Confidence threshold loaded: 0.8
loadSettings: Speaker diarization loaded: true
loadSettings: Music detection loaded: true
loadSettings: SFX detection loaded: false
loadSettings: Disable mock data loaded: false
loadSettings: Settings loading completed successfully
MainWindow: Settings loaded successfully
=== MainWindow: Starting immediate UI initialization ===
MainWindow: Window geometry before setupUI: QRect(0,0 1200x800)
MainWindow: Window size before setupUI: QSize(1200, 800)
MainWindow: Starting setupUI()
=== setupUI: Starting UI creation ===
setupUI: Window size before UI creation: QSize(1200, 800)
setupUI: Window geometry before UI creation: QRect(0,0 1200x800)
setupUI: Creating central widget
setupUI: Central widget created: QWidget(0x15987c950)
setupUI: Central widget set successfully
setupUI: Central widget size: QSize(640, 480)
setupUI: Central widget visible: false
setupUI: Creating main layout
setupUI: Main layout created and set on central widget
setupUI: Creating progress tracking group
setupUI: Progress tracking group created: QGroupBox(0x15987d270)
setupUI: Progress tracking group added to layout
setupUI: Creating main splitter
setupUI: Main splitter created: QSplitter(0x1599333c0)
setupUI: Creating left panel
setupUI: Left panel created: QWidget(0x138eaa290)
setupUI: Left layout created
setupUI: Creating file operations group
setupUI: File operations group created: QGroupBox(0x138ea6310)
setupUI: Creating classification progress group
setupUI: Classification progress group created: QGroupBox(0x158ec1060)
setupUI: Creating memory management group
setupUI: Memory management group created: QGroupBox(0x159936640)
setupUI: Left panel setup completed successfully
setupUI: Creating tab widget
setupUI: Tab widget created: QTabWidget(0x1598ddb30)
setupUI: Creating Timeline tab
createTimelineTab: Starting timeline tab creation
createTimelineTab: Creating TimelineWidget
createTimelineTab: TimelineWidget created successfully
createTimelineTab: Setting core components
createTimelineTab: Creating wrapper widget and layout
createTimelineTab: Wrapper widget and layout created
createTimelineTab: Adding timeline widget to main layout
createTimelineTab: Timeline tab creation completed successfully
setupUI: Timeline tab created: QWidget(0x158eccf10)
setupUI: Timeline tab added successfully
setupUI: Creating Classification tab
createClassificationTab: Starting
createClassificationTab: Creating classification controls
createClassificationTab: Creating buttons
createClassificationTab: Setting button states
createClassificationTab: Connecting button signals
createClassificationTab: Adding buttons to layout
createClassificationTab: Creating status and progress
createClassificationTab: Creating results table
createClassificationTab: Creating result buttons
createClassificationTab: Connecting review button signal
createClassificationTab: Adding to main layout
createClassificationTab: Completed successfully
setupUI: Classification tab created: QWidget(0x15996ba40)
setupUI: Classification tab added successfully
setupUI: Creating Organization tab
Organization table created with built-in sorting enabled
setupUI: Organization tab created: QWidget(0x159a05170)
setupUI: Organization tab added successfully
setupUI: Creating Export tab
setupUI: Export tab created: QWidget(0x115e3bcf0)
setupUI: Export tab added successfully
setupUI: Creating Log tab
setupUI: Log tab created: QGroupBox(0x115e85e20)
setupUI: Log tab added successfully
setupUI: All tabs created and added successfully
setupUI: Tab widget has 5 tabs
setupUI: Adding panels to splitter
setupUI: Left panel size: QSize(400, 480)
setupUI: Tab widget size: QSize(640, 480)
setupUI: Left panel added to splitter
setupUI: Tab widget added to splitter
setupUI: Splitter stretch factors set
setupUI: Adding splitter to main layout
setupUI: Splitter added to main layout successfully
setupUI: Final central widget size: QSize(640, 480)
setupUI: Final window size: QSize(1200, 800)
=== setupUI: UI creation completed successfully ===
MainWindow: setupUI() completed successfully
MainWindow: Central widget after setupUI: QWidget(0x15987c950)
MainWindow: Window size after setupUI: QSize(1200, 800)
MainWindow: Setting up menu bar
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
MainWindow: Menu bar setup completed
MainWindow: Menu bar visible: false
MainWindow: Setting up status bar
MainWindow: Status bar setup completed
MainWindow: Status bar visible: false
MainWindow: Setting up status timer
MainWindow: Status timer started
=== MainWindow: Checking widget hierarchy after setup ===
MainWindow: Central widget: QWidget(0x15987c950)
MainWindow: Widget size: QSize(1200, 800)
MainWindow: Widget minimum size: QSize(1200, 800)
MainWindow: Widget is visible: false
MainWindow: Widget window flags: QFlags<Qt::WindowType>(Window|WindowTitleHint|WindowSystemMenuHint|WindowMinMaxButtonsHint|WindowCloseButtonHint)
MainWindow: Widget has focus: false
MainWindow: Widget is enabled: true
MainWindow: Widget window state: QFlags<Qt::WindowState>(WindowNoState)
=== MainWindow: UI initialization completed successfully ===
=== MainWindow: Forcing window visibility in constructor ===
MainWindow: Before show() - visible: false geometry: QRect(0,0 1200x800)
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
MainWindow: After show() - visible: true geometry: QRect(264,175 1200x800)
MainWindow: After raise() - visible: true active: false
MainWindow: After activateWindow() - visible: true active: false
MainWindow: After repaint() - visible: true
=== MainWindow: Constructor completed successfully ===
MainWindow object created at address: MainWindow(0x15a020800)
=== SETTING CORE COMPONENTS ===
Memory manager set for AAFReader
Phase 4 AI components enabled
LibAAF wrapper set for AAFReader
Python bridge set for AAFReader (deprecated)
Python bridge set for AudioAnalyzer
Python bridge set for AudioFileManager
Python bridge set for ClassificationEngine
Analysis settings set for ClassificationEngine
Progress tracker set for ClassificationEngine
Audio file manager set for ClassificationEngine
TimelineWidget::setAAFReader: Starting
TimelineWidget::setAAFReader: Completed
TimelineWidget::setAudioFileManager: Starting
TimelineWidget::setAudioFileManager: Completed
TimelineWidget::setAudioPlaybackManager: Starting
TimelineWidget::setAudioPlaybackManager: Completed
LMStudioClient set for TrackOrganizer: Available
AAFExporter: LibAAF wrapper set for consistent AAF handling
AAF Exporter configured with LibAAF wrapper for consistent AAF handling
LM Studio server URL changed to: "http://*************:5001"
Core components set successfully
=== WINDOW VISIBILITY DEBUGGING ===
Window geometry before show: QRect(264,175 1200x800)
Window size hint: QSize(1503, 954)
Window minimum size: QSize(1200, 800)
Window is visible before show: true
Window is window: true
Window window state: QFlags<Qt::WindowState>(WindowNoState)
Window window flags: QFlags<Qt::WindowType>(Window|WindowTitleHint|WindowSystemMenuHint|WindowMinMaxButtonsHint|WindowCloseButtonHint)
Available screen geometry: QRect(0,32 1728x1085)
Setting window size and position...
Window geometry after resize/move: QRect(264,202 1200x800)
=== FORCING WINDOW VISIBILITY ===
Method 1: Calling show()...
qt.qpa.fonts: Populating font family aliases took 184 ms. Replace uses of missing font family "Monaco, Consolas, Monospace" with one that exists to avoid this cost. 
After show() - Visible: true Active: false
Method 2: Calling raise() and activateWindow()...
After raise/activate - Visible: true Active: true
Method 3: Forcing window to front...
After force front - Visible: true Active: true
Method 4: macOS-specific window activation...
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
After macOS activation - Visible: true Active: true
Method 5: Force repaint and update...
=== FINAL WINDOW STATE ===
Final window state - Visible: true Active: true Geometry: QRect(264,202 1200x800) WindowState: QFlags<Qt::WindowState>(WindowNoState) Window Handle: 5787014336
=== WAAFer APPLICATION STARTUP COMPLETED ===
Entering event loop...
Timer check - Window visible: true
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
Loading AAF file: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
AAFReader: LibAAF wrapper available, attempting to parse AAF file: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
[[38;5;124m error [0m] [38;5;242mAAFCore.c:1689 in retrieveMetaDictionaryClass()[0m : Could not retrieve TypeDefinition from dictionary.
[[38;5;124m error [0m] [38;5;242mAAFCore.c:1689 in retrieveMetaDictionaryClass()[0m : Could not retrieve TypeDefinition from dictionary.
[[38;5;124m error [0m] [38;5;242mAAFCore.c:1689 in retrieveMetaDictionaryClass()[0m : Could not retrieve TypeDefinition from dictionary.
[[38;5;124m error [0m] [38;5;242mAAFCore.c:1689 in retrieveMetaDictionaryClass()[0m : Could not retrieve TypeDefinition from dictionary.
[[38;5;124m error [0m] [38;5;242mAAFCore.c:1033 in aaf_get_property()[0m : Could not retrieve AAFClassID_ContentStorage required property 0x1902 (PID_ContentStorage_EssenceData)
LibAAFWrapper: Successfully parsed AAF file: tracks: 64 regions: 700 duration: 95.84 seconds
AAFReader: LibAAF parseAAFFile returned - isValid: true trackCount: 64 regionCount: 700 duration: 95.84 frameRate: 25
AAFReader: Extracting tracks using LibAAF from file: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
LibAAFWrapper: Found 64 audio tracks
AAFReader: LibAAF returned 64 tracks
AAFReader: Added track: "PGM L / IZJAVA" number: 1 clips: 13
AAFReader: Added track: "PGM R / IZJAVA" number: 2 clips: 15
AAFReader: Added track: "VODITELJA" number: 3 clips: 12
AAFReader: Added track: "VODITELJA" number: 4 clips: 10
AAFReader: Added track: "SFX" number: 5 clips: 7
AAFReader: Added track: "SFX" number: 6 clips: 6
AAFReader: Added track: "SFX" number: 7 clips: 5
AAFReader: Added track: "SFX" number: 8 clips: 5
AAFReader: Added track: "GLASBA" number: 9 clips: 7
AAFReader: Added track: "GLASBA" number: 10 clips: 7
AAFReader: Added track: "GLASBA" number: 11 clips: 3
AAFReader: Added track: "GLASBA" number: 12 clips: 3
AAFReader: Added track: "GLASBA" number: 13 clips: 5
AAFReader: Added track: "GLASBA" number: 14 clips: 5
AAFReader: Added track: "LADO" number: 15 clips: 14
AAFReader: Added track: "MARJETKA" number: 16 clips: 15
AAFReader: Added track: "ANA" number: 17 clips: 15
AAFReader: Added track: "ANDREJ" number: 18 clips: 15
AAFReader: Added track: "HAND 1" number: 19 clips: 14
AAFReader: Added track: "HAND 2" number: 20 clips: 14
AAFReader: Added track: "HAND 3" number: 21 clips: 14
AAFReader: Added track: "HAND 4" number: 22 clips: 14
AAFReader: Added track: "HAND 5" number: 23 clips: 14
AAFReader: Added track: "HAND 6" number: 24 clips: 14
AAFReader: Added track: "HAND 7" number: 25 clips: 14
AAFReader: Added track: "HAND 8" number: 26 clips: 14
AAFReader: Added track: "MATRICA L" number: 27 clips: 14
AAFReader: Added track: "MATRICA R" number: 28 clips: 14
AAFReader: Added track: "KEY" number: 29 clips: 14
AAFReader: Added track: "KEY" number: 30 clips: 14
AAFReader: Added track: "INSTR 1" number: 31 clips: 14
AAFReader: Added track: "INSTR 2" number: 32 clips: 14
AAFReader: Added track: "INSTR 3" number: 33 clips: 14
AAFReader: Added track: "INSTR 4" number: 34 clips: 14
AAFReader: Added track: "KICK" number: 35 clips: 14
AAFReader: Added track: "SNARE" number: 36 clips: 14
AAFReader: Added track: "OH" number: 37 clips: 14
AAFReader: Added track: "OH" number: 38 clips: 14
AAFReader: Added track: "BASS" number: 39 clips: 14
AAFReader: Added track: "GTR" number: 40 clips: 14
AAFReader: Added track: "GTR" number: 41 clips: 14
AAFReader: Added track: "BABIC" number: 42 clips: 14
AAFReader: Added track: "TBA" number: 43 clips: 14
AAFReader: Added track: "AMB SPOT" number: 44 clips: 14
AAFReader: Added track: "AMBI HALL L" number: 45 clips: 14
AAFReader: Added track: "AMBI HALL R" number: 46 clips: 14
AAFReader: Added track: "AMBI STAGE L" number: 47 clips: 14
AAFReader: Added track: "AMBI STAGE R" number: 48 clips: 14
AAFReader: Added track: "TBA" number: 49 clips: 14
AAFReader: Added track: "TBA" number: 50 clips: 14
AAFReader: Added track: "TBA" number: 51 clips: 14
AAFReader: Added track: "BUZZ" number: 52 clips: 14
AAFReader: Added track: "PGM L" number: 53 clips: 14
AAFReader: Added track: "PGM R" number: 54 clips: 11
AAFReader: Added track: "ZIR A" number: 55 clips: 11
AAFReader: Added track: "ZIR B" number: 56 clips: 11
AAFReader: Added track: "ZIR C" number: 57 clips: 11
AAFReader: Added track: "ZIR D" number: 58 clips: 1
AAFReader: Added track: "NC" number: 59 clips: 1
AAFReader: Added track: "NC" number: 60 clips: 1
AAFReader: Added track: "Track_61" number: 61 clips: 1
AAFReader: Added track: "Track_62" number: 62 clips: 0
AAFReader: Added track: "Track_63" number: 63 clips: 0
AAFReader: Added track: "Track_64" number: 64 clips: 0
Extracted 64 tracks
AAFReader: Extracting regions using LibAAF from file: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
LibAAFWrapper: Found 700 audio regions
AAFReader: LibAAF returned 700 regions
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 20.68 duration: 1.8 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED766EAEC0DA.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 23.88 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 25.12 duration: 1.4 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED766EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED966EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 31.04 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 33.52 duration: 2.28 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 35.8 duration: 0.52 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC466EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 44.24 duration: 5.2 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "PGM L / IZJAVA" start: 92.84 duration: 3 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/FREMANTLE-A01.66EA66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 20.68 duration: 1.8 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED766EAEC0DA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 23.88 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 25.12 duration: 1.4 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED766EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED966EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 35.8 duration: 0.72 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 36.52 duration: 2.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC466EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 44.24 duration: 5.2 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "PGM R / IZJAVA" start: 92.84 duration: 3 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/FREMANTLE-A02.66EA66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED866EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 17.84 duration: 0.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECB66EAEBE6A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC066EAEBC4A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 35.8 duration: 0.72 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 36.52 duration: 2.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC566EAEBD6A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 45.68 duration: 0.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/BBCPM_022_A01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 49.24 duration: 25.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 74.32 duration: 16.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED866EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC066EAEBC4A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 35.8 duration: 0.72 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 36.52 duration: 2.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC566EAEBD6A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 45.68 duration: 0.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/BBCPM_022_A02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 49.24 duration: 25.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "VODITELJA" start: 74.32 duration: 16.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED566EAEC06A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEC06A.1.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEC06A.1.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 22.08 duration: 2.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBD66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 35.8 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED566EAEC05A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 44.24 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/DROP%20-%20BenA01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED566EAEC06A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEC06A.1.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEC06A.1.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 22.08 duration: 2.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBD66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 35.8 duration: 3.2 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED566EAEC05A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 44.24 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/DROP%20-%20BenA02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 14.56 duration: 3.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/WOOSH%202.mpA01.66EA66EAEBB8A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 32.64 duration: 3.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A01.66EA66EAEBB9A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 36.64 duration: 2.2 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/Sudden%20ImpA03.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 44.24 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/Audio%20fileA01.66EA66EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 48.64 duration: 2.12 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A01.66EAEBB9A.1.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 14.56 duration: 3.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/WOOSH%202.mpA02.66EA66EAEBB8A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 32.64 duration: 3.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A02.66EA66EAEBB9A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 36.64 duration: 2.2 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/Sudden%20ImpA03.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 44.24 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/Audio%20fileA02.66EA66EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "SFX" start: 48.64 duration: 2.12 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A02.66EAEBB9A.1.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 7.2 duration: 3.72 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECB66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 9.92 duration: 5.8 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/SIT_aplavzA01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 21.28 duration: 3.52 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A01.66EAEBB9A.2.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOT501A01.66EA66EAEBB9A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 44.24 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/BBCPM_022_A01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 49.28 duration: 16.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBE66EAEBBFA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 87.04 duration: 5.8 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBE66EAEBC0A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 7.2 duration: 3.72 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECB66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 9.92 duration: 5.8 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/SIT_aplavzA02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 21.28 duration: 3.52 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A02.66EAEBB9A.2.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOT501A02.66EA66EAEBB9A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 44.24 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/BBCPM_022_A02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 49.28 duration: 16.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBE66EAEBBFA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 87.04 duration: 5.8 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBE66EAEBC0A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 11.4 duration: 5.56 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOK242A01.66EA66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 20.92 duration: 28.12 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC166EAEBC8A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 65.52 duration: 22.52 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBE66EAEBBFA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 11.4 duration: 5.56 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOK242A02.66EA66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 20.92 duration: 28.12 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC166EAEBC8A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 65.52 duration: 22.52 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBE66EAEBBFA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 0 duration: 11 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOK242A01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 11 duration: 2.4 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOK242A01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 14.52 duration: 12.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_ARX34_A01.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 34.76 duration: 3.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A01.66EAEBB9A.3.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 46.12 duration: 3.56 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC166EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 0 duration: 11 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOK242A02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 11 duration: 2.4 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_KOK242A02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 14.52 duration: 12.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_ARX34_A02.66EA66EAEBBAA.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 34.76 duration: 3.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UPM_SVN77_A02.66EAEBB9A.3.wav"
AAFReader: Added region: "Clip" on track: "GLASBA" start: 46.12 duration: 3.56 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC166EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED866EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED766EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED966EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 31.04 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC466EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 49.24 duration: 25.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBC0A.1.wav"
AAFReader: Added region: "Clip" on track: "LADO" start: 74.32 duration: 16.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBC0A.1.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED866EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED766EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED966EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC466EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 49.24 duration: 25.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBC0A.1.wav"
AAFReader: Added region: "Clip" on track: "MARJETKA" start: 74.32 duration: 16.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBC0A.1.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED966EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC466EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 49.24 duration: 25.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBE66EAEBC0A.wav"
AAFReader: Added region: "Clip" on track: "ANA" start: 74.32 duration: 16.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBE66EAEBC0A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED866EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED866EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED966EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC566EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 49.24 duration: 25.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBF66EAEBC0A.wav"
AAFReader: Added region: "Clip" on track: "ANDREJ" start: 74.32 duration: 16.08 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBF66EAEBC0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED966EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED866EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEDA66EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC566EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 1" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED966EAEC11A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED966EAEC12A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED866EAEC0EA.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEDA66EAEC14A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECA66EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED866EAEC0FA.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEDA66EAEC15A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC566EAEBD3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 2" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECA66EAEBE3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC366EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBE9A.1.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC266EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBE9A.1.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED366EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBE9A.1.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEBE66EAEBBEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBE9A.1.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBF66EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 3" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC366EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC266EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC666EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBF66EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 4" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC366EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC266EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC666EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBF66EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 5" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC366EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC266EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC666EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBF66EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 6" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC366EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC266EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC666EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEBF66EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 7" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC466EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC366EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC666EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC066EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "HAND 8" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC466EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC366EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC666EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC066EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA L" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC466EAEBD0A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC466EAEBD1A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC366EAEBCCA.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED466EAEC02A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC766EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECC66EAEBE9A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC366EAEBCEA.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED466EAEC04A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC066EAEBC3A.wav"
AAFReader: Added region: "Clip" on track: "MATRICA R" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECC66EAEBEBA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC866EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC766EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBC9A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC766EAEBD8A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED066EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC866EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC766EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBC9A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "KEY" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC866EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC766EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC166EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 1" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC866EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC766EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC166EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66E66EAEBF7A.1.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 2" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC866EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC766EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC166EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 3" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC866EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC766EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC266EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "INSTR 4" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC966EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC866EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC266EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEBD66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "KICK" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC966EAEBDEA.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC966EAEBE0A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC866EAEBDBA.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC266EAEBC9A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC866EAEBDDA.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC266EAEBCBA.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEBE66EAEBBCA.wav"
AAFReader: Added region: "Clip" on track: "SNARE" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED166EAEBF9A.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED666EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECC66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEC07A.1.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECC66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECC66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC566EAEBD4A.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECC66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED266EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED666EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEC07A.1.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBD6A.1.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "OH" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED666EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEC07A.1.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBD6A.1.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBE7A.2.wav"
AAFReader: Added region: "Clip" on track: "BASS" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED766EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED666EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC666EAEBD6A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED766EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED666EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC666EAEBD7A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "GTR" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED766EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED666EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC666EAEBD7A.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "BABIC" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED766EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED666EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC666EAEBD7A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED766EAEC0AA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED766EAEC0CA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED666EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED266EAEBFEA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC666EAEBD7A.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECD66EAEBEDA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED666EAEC09A.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED366EAEBFFA.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "AMB SPOT" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECD66EAEBEEA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECE66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECD66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC666EAEBD7A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC066EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL L" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED166EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBC1A.1.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "AMBI HALL R" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBC5A.1.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBC1A.1.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE L" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "AMBI STAGE R" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "TBA" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECF66EAEBF3A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECF66EAEBF4A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECE66EAEBF0A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA07.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED166EAEBFBA.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAECE66EAEBF1A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEC166EAEBC7A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED366EAEC01A.wav"
AAFReader: Added region: "Clip" on track: "BUZZ" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAED266EAEBFDA.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 0 duration: 8.44 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED566EAEC06A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 8.44 duration: 3.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEC06A.1.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 11.68 duration: 2.04 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEC06A.1.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED466EAEC05A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC066EAEBC4A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA08.66EAEBF66EAEBC1A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECB66EAEBE7A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED566EAEC05A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC066EAEBC5A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED566EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "PGM L" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBE7A.1.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECF66EAEBF6A.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECA66EAEBE5A.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBBBA.1.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAECA66EAEBE6A.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED566EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "PGM R" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66E66EAEBDAA.1.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED066EAEBF6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECA66EAEBE5A.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBBBA.1.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAECA66EAEBE6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA01.66EAED566EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "ZIR A" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66E66EAEBDAA.1.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED066EAEBF6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECA66EAEBE5A.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEBD66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAECB66EAEBE6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA02.66EAED566EAEC07A.wav"
AAFReader: Added region: "Clip" on track: "ZIR B" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66E66EAEBDAA.1.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 17.84 duration: 2.84 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 23.76 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED066EAEBF6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 26.52 duration: 1.64 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 28.16 duration: 2.88 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECA66EAEBE5A.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 31.04 duration: 1.6 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 32.64 duration: 1.92 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEBD66EAEBBBA.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 34.56 duration: 1.24 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC766EAEBDAA.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 35.8 duration: 3.32 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAED066EAEBF7A.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 39.12 duration: 2.76 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAECB66EAEBE6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 41.88 duration: 2.36 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC566EAEBD6A.wav"
AAFReader: Added region: "Clip" on track: "ZIR C" start: 44.24 duration: 5 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66E66EAEBDAA.1.wav"
AAFReader: Added region: "Clip" on track: "ZIR D" start: 33.52 duration: 2.28 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA03.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "NC" start: 33.52 duration: 2.28 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA04.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "NC" start: 33.52 duration: 2.28 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA05.66EAEC966EAEBE1A.wav"
AAFReader: Added region: "Clip" on track: "" start: 33.52 duration: 2.28 audioFile: "file:///D%3A/SIT10%20AAF/AVD01_6DEL_TEASER_PRIHODNJIC/OMFI%20MediaFiles/UnnamedA06.66EAEC966EAEBE1A.wav"
Extracted 700 regions
Allocated chunk "aaf_data_SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro" size: 51200 KB
Allocated 50 MB for AAF data processing
Current AAF file set for ClassificationEngine: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
Set AAF file path in ClassificationEngine: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
TimelineWidget::loadTimelineData: Loaded 60 tracks, 700 regions, duration: 300
TimelineWidget::loadTimelineData: UI update completed
AAF file loaded successfully: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x3234e78c0> contents scale of 1.57655 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x168a65d40> contents scale of 1.57655 - updating layer to match.
TimelineWidget::forceRefresh: Forcing complete timeline refresh
TimelineWidget::forceRefresh: Refresh completed
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.57655 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x1695b4310> contents scale of 1.57655 - updating layer to match.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.57655 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.57655 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x1695b4310> contents scale of 1.57655 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.43919 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.43919 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.31379 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.31379 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.34088 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.34088 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.36854 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.36854 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.39677 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.39677 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.42557 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.42557 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.30135 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.30135 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.18797 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.18797 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.08446 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.08446 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.21247 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.21247 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x1695b4310> contents scale of 1.21247 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.35557 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.35557 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x115ee0030> contents scale of 1.69447 - updating layer to match.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x128d08f50> contents scale of 1.69447 - updating layer to match.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.backingstore: Back buffer dpr of 2 doesn't match <NSViewBackingLayer: 0x1695b4310> contents scale of 1.21247 - updating layer to match.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
TimelineWidget: Toggled region name source to Source B (Descriptions)
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
TimelineWidget: Toggled region name source to Source B (Descriptions)
TimelineWidget: Toggled region name source to Source A (File Names)
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
Closing AAF file: "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
Finalizing Python bridge...
Finalizing Python interpreter...
Python interpreter finalized
Python bridge finalized
MainWindow destroyed
Network error for "availability" request: QNetworkReply::OperationCanceledError "Operation canceled"
LM Studio server check failed: "Operation canceled"
Analysis settings saved
Clearing all cached data
