#!/usr/bin/env python3
"""
Comprehensive test script for WAAFer Timeline fixes
"""

import os
import sys
import json
import tempfile
from pathlib import Path

def test_aaf_parsing_with_stereo_detection():
    """Test AAF parsing to verify stereo channel detection"""
    print("=== Testing AAF Parsing with Stereo Detection ===")
    
    aaf_file = "/Volumes/Projects/AAF/AVD01_AAF/SIT10_EP01_6DEL_TEASER_PRIHODNJIC_sinhro.aaf"
    
    if not os.path.exists(aaf_file):
        print(f"❌ Test AAF file not found: {aaf_file}")
        return False
    
    try:
        import aaf2
        print("✓ aaf2 library available")
        
        with aaf2.open(aaf_file, 'r') as f:
            print("✓ AAF file opened successfully")
            
            # Get composition mobs
            comp_mobs = list(f.content.compositionmobs())
            if not comp_mobs:
                print("❌ No composition mobs found")
                return False
                
            comp_mob = comp_mobs[0]
            print(f"✓ Using composition mob: {comp_mob.name}")
            
            # Analyze tracks for stereo pairs
            audio_tracks = []
            for slot in comp_mob.slots:
                if hasattr(slot, 'segment') and slot.segment:
                    if slot.name and 'Track' in slot.name:
                        audio_tracks.append({
                            'name': slot.name,
                            'slot_id': slot.slot_id,
                            'edit_rate': slot.edit_rate
                        })
            
            print(f"✓ Found {len(audio_tracks)} audio tracks")
            
            # Check for stereo pairs (consecutive tracks)
            stereo_pairs = []
            for i in range(0, len(audio_tracks), 2):
                if i + 1 < len(audio_tracks):
                    left_track = audio_tracks[i]
                    right_track = audio_tracks[i + 1]
                    stereo_pairs.append({
                        'left': left_track,
                        'right': right_track,
                        'group_id': f"StereoGroup_{(i // 2) + 1}"
                    })
            
            print(f"✓ Detected {len(stereo_pairs)} potential stereo pairs")
            
            # Verify our stereo detection logic matches the AAF structure
            if len(stereo_pairs) >= 6:  # Should have at least 6 stereo pairs for 12 regions
                print("✓ Stereo pair detection working correctly")
                return True
            else:
                print(f"⚠️  Expected at least 6 stereo pairs, found {len(stereo_pairs)}")
                return False
                
    except ImportError:
        print("❌ aaf2 library not available")
        return False
    except Exception as e:
        print(f"❌ Error testing AAF parsing: {e}")
        return False

def test_export_data_size():
    """Test export data handling for large files"""
    print("\n=== Testing Export Data Size Handling ===")
    
    # Create mock data similar to the 939 regions in the test file
    mock_regions = []
    
    for i in range(939):  # Same number as in the test AAF
        region = {
            "id": f"region_{i:03d}",
            "name": f"UnnamedA{i:02d}.66EAEC966EAEBE1A",
            "alternativeName": f"2024-06-22_SIT-AVD05-2206_{i}(A).new.{i:02d}",
            "trackName": f"Track {(i % 66) + 1}",
            "position": i * 2.5,
            "length": 2.0,
            "contentType": ["Dialogue", "Music", "SFX", "Ambience"][i % 4],
            "speakerId": f"Speaker_{(i % 10) + 1}",
            "confidence": 0.85,
            "channelNumber": (i % 2) + 1,
            "stereoGroupId": f"StereoGroup_{(i // 2) + 1}",
            "audioFilePath": f"/path/to/audio_{i:03d}.wav"
        }
        mock_regions.append(region)
    
    # Test JSON serialization size
    regions_json = json.dumps(mock_regions, separators=(',', ':'))
    total_size = len(regions_json)
    
    print(f"✓ Mock data created: {len(mock_regions)} regions")
    print(f"✓ JSON data size: {total_size:,} bytes")
    
    # Check if this would exceed command line limits
    arg_max = 262144  # 256KB typical limit
    if total_size > arg_max:
        print(f"✓ Data size ({total_size:,} bytes) exceeds ARG_MAX ({arg_max:,} bytes)")
        print("✓ Temporary file approach is necessary")
        return True
    else:
        print(f"⚠️  Data size ({total_size:,} bytes) is within ARG_MAX limits")
        return True

def run_all_tests():
    """Run all tests and report results"""
    print("WAAFer Timeline Fixes - Test Suite")
    print("=" * 50)
    
    tests = [
        ("AAF Parsing with Stereo Detection", test_aaf_parsing_with_stereo_detection),
        ("Export Data Size Handling", test_export_data_size)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    return passed == len(results)

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
