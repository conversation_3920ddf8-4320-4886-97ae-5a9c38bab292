#include "AAFExporter.h"
#include "../python/PythonBridge.h"
#include "../core/AAFReader.h"
#include "../core/LibAAFWrapper.h"
#include <QFileInfo>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTextStream>
#include <QDateTime>
#include <QRegularExpression>
#include <QStandardPaths>
#include <QFile>

AAFExporter::AAFExporter(QObject *parent)
    : QObject(parent)
    , m_pythonBridge(nullptr)
    , m_aafReader(nullptr)
    , m_libAAFWrapper(nullptr)
    , m_isExporting(false)
    , m_progress(0.0)
    , m_progressTimer(new QTimer(this))
    , m_currentStep(0)
    , m_totalSteps(0)
{
    // Setup progress timer
    m_progressTimer->setInterval(100); // Update every 100ms
    connect(m_progressTimer, &QTimer::timeout, this, &AAFExporter::updateProgress);
    
    qDebug() << "AAFExporter initialized";
}

AAFExporter::~AAFExporter()
{
    if (m_isExporting) {
        cancelExport();
    }
}

void AAFExporter::setPythonBridge(PythonBridge *bridge)
{
    m_pythonBridge = bridge;
}

void AAFExporter::setAAFReader(AAFReader *reader)
{
    m_aafReader = reader;
}

void AAFExporter::setLibAAFWrapper(LibAAFWrapper *wrapper)
{
    m_libAAFWrapper = wrapper;
    qDebug() << "AAFExporter: LibAAF wrapper set for consistent AAF handling";
}

bool AAFExporter::exportRegions(const QVariantList &regions,
                                const QVariantList &tracks,
                                const QString &outputPath,
                                const ExportOptions &options)
{
    if (m_isExporting) {
        qWarning() << "Export already in progress";
        return false;
    }
    
    // Store export parameters
    m_currentRegions = regions;
    m_currentTracks = tracks;
    m_currentOutputPath = outputPath;
    m_currentOptions = options;
    
    // Validate parameters
    if (!validateExportParameters()) {
        return false;
    }
    
    qDebug() << "Starting export to:" << outputPath << "format:" << options.format;
    
    // Reset state and start export
    resetState();
    m_isExporting = true;
    m_progressTimer->start();
    
    // Determine total steps based on format
    switch (options.format) {
    case AAF:
        m_totalSteps = 6; // Validate, prepare, create AAF, add tracks, add regions, finalize
        break;
    case OMF:
        m_totalSteps = 5; // Validate, prepare, create OMF, add tracks, add regions
        break;
    case XML:
        m_totalSteps = 4; // Validate, prepare, generate XML, write file
        break;
    case CSV:
    case JSON:
        m_totalSteps = 3; // Validate, prepare, write file
        break;
    }
    
    setProgress(0.0, "Starting export...");
    
    // Start export process based on format
    bool success = false;
    switch (options.format) {
    case AAF:
        success = exportToAAF();
        break;
    case OMF:
        success = exportToOMF();
        break;
    case XML:
        success = exportToXML();
        break;
    case CSV:
        success = exportToCSV();
        break;
    case JSON:
        success = exportToJSON();
        break;
    }
    
    if (!success) {
        resetState();
        return false;
    }
    
    return true;
}

bool AAFExporter::isExporting() const
{
    return m_isExporting;
}

double AAFExporter::progress() const
{
    return m_progress;
}

QString AAFExporter::statusMessage() const
{
    return m_statusMessage;
}

void AAFExporter::cancelExport()
{
    if (m_isExporting) {
        qDebug() << "Cancelling export";
        resetState();
        emit exportFailed("Export cancelled by user");
    }
}

void AAFExporter::updateProgress()
{
    if (!m_isExporting) {
        m_progressTimer->stop();
        return;
    }
    
    // Simulate progress updates for demonstration
    // In real implementation, this would track actual export progress
    if (m_currentStep < m_totalSteps) {
        m_currentStep++;
        double newProgress = static_cast<double>(m_currentStep) / m_totalSteps;
        
        QString message;
        switch (m_currentStep) {
        case 1:
            message = "Validating export parameters...";
            break;
        case 2:
            message = "Preparing regions for export...";
            break;
        case 3:
            if (m_currentOptions.format == AAF) {
                message = "Creating AAF file structure...";
            } else {
                message = "Generating export data...";
            }
            break;
        case 4:
            if (m_currentOptions.format == AAF) {
                message = "Adding tracks to AAF...";
            } else {
                message = "Writing export file...";
            }
            break;
        case 5:
            message = "Adding regions to AAF...";
            break;
        case 6:
            message = "Finalizing AAF file...";
            break;
        default:
            message = "Processing...";
            break;
        }
        
        setProgress(newProgress, message);
        
        // Complete export when all steps are done
        if (m_currentStep >= m_totalSteps) {
            QTimer::singleShot(500, [this]() {
                setProgress(1.0, "Export completed successfully");
                m_progressTimer->stop();
                m_isExporting = false;
                emit exportCompleted(m_currentOutputPath);
            });
        }
    }
}

bool AAFExporter::exportToAAF()
{
    // Prefer LibAAFWrapper for consistent AAF handling
    if (m_libAAFWrapper && m_libAAFWrapper->isInitialized()) {
        qDebug() << "AAFExporter: Using LibAAFWrapper for consistent AAF export";
        return exportToAAFWithLibAAF();
    }

    // Fallback to Python bridge
    if (!m_pythonBridge || !m_pythonBridge->isInitialized()) {
        emit exportFailed("Neither LibAAF wrapper nor Python bridge available for AAF export");
        return false;
    }

    if (!m_aafReader) {
        emit exportFailed("AAF reader not available");
        return false;
    }

    qDebug() << "AAFExporter: Falling back to Python bridge for AAF export";
    return exportToAAFWithPython();
}

bool AAFExporter::exportToAAFWithLibAAF()
{
    if (!m_libAAFWrapper || !m_libAAFWrapper->isInitialized()) {
        emit exportFailed("LibAAF wrapper not available or not initialized");
        return false;
    }

    if (!m_pythonBridge || !m_pythonBridge->isInitialized()) {
        emit exportFailed("Python bridge required for AAF export execution");
        return false;
    }

    if (!m_aafReader) {
        emit exportFailed("AAF reader not available");
        return false;
    }

    qDebug() << "AAFExporter: Starting LibAAF-compatible AAF export";

    // Prepare regions for export using LibAAF-compatible data structures
    QVariantList exportRegions = prepareRegionsForExport();
    QVariantMap metadata = generateExportMetadata();

    // Generate LibAAF-compatible export script
    QString pythonScript = m_libAAFWrapper->generateLibAAFCompatibleExportScript(
        m_currentOutputPath, exportRegions, m_currentTracks, metadata);

    // Execute the LibAAF-compatible script through Python bridge
    qDebug() << "AAFExporter: Executing LibAAF-compatible Python script";
    QString result = m_pythonBridge->executePythonCommand(pythonScript);
    qDebug() << "AAFExporter: LibAAF-compatible script result:" << result;

    // Check for library import errors
    if (result.contains("EXPORT_ERROR: Missing required library")) {
        QString error = "AAF library (pyaaf2) not installed. Please install with: pip install pyaaf2";
        qCritical() << "AAFExporter:" << error;
        emit exportFailed(error);
        return false;
    }

    if (result.contains("EXPORT_RESULT: True")) {
        qDebug() << "AAFExporter: LibAAF-compatible export successful";
        emit exportCompleted(m_currentOutputPath);
        return true;
    } else {
        QString error = result.contains("EXPORT_RESULT: False") ?
                       result.split("|").last().trimmed() :
                       QString("LibAAF-compatible AAF export failed. Result: %1").arg(result);
        qCritical() << "AAFExporter:" << error;
        emit exportFailed(error);
        return false;
    }
}

bool AAFExporter::exportToAAFWithPython()
{
    qDebug() << "AAFExporter: Starting Python-based AAF export (fallback)";

    // Prepare regions for export
    QVariantList exportRegions = prepareRegionsForExport();
    QVariantMap metadata = generateExportMetadata();

    // Create temporary files for large data to avoid "Argument list too long" error
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QString regionsFile = QDir(tempDir).filePath("waafer_export_regions.json");
    QString tracksFile = QDir(tempDir).filePath("waafer_export_tracks.json");
    QString metadataFile = QDir(tempDir).filePath("waafer_export_metadata.json");

    // Write data to temporary files
    QJsonDocument regionsDoc = QJsonDocument::fromVariant(exportRegions);
    QJsonDocument tracksDoc = QJsonDocument::fromVariant(m_currentTracks);
    QJsonDocument metadataDoc = QJsonDocument::fromVariant(metadata);

    QFile regionsFileObj(regionsFile);
    QFile tracksFileObj(tracksFile);
    QFile metadataFileObj(metadataFile);

    if (!regionsFileObj.open(QIODevice::WriteOnly) ||
        !tracksFileObj.open(QIODevice::WriteOnly) ||
        !metadataFileObj.open(QIODevice::WriteOnly)) {
        emit exportFailed("Failed to create temporary files for export data");
        return false;
    }

    regionsFileObj.write(regionsDoc.toJson(QJsonDocument::Compact));
    tracksFileObj.write(tracksDoc.toJson(QJsonDocument::Compact));
    metadataFileObj.write(metadataDoc.toJson(QJsonDocument::Compact));

    regionsFileObj.close();
    tracksFileObj.close();
    metadataFileObj.close();

    qDebug() << "AAFExporter: Created temporary files:"
             << "regions:" << regionsFile
             << "tracks:" << tracksFile
             << "metadata:" << metadataFile;

    // Create Python script for AAF export using file-based data
    QString pythonScript = QString(R"(
# Check for AAF library first
try:
    import aaf2
except ImportError as e:
    print(f"EXPORT_ERROR: Missing AAF library: {str(e)}")
    print("EXPORT_ERROR: Please install AAF library: pip install pyaaf2")
    exit(1)

import os
import json
from pathlib import Path

def export_aaf_file(source_file, output_file, regions_data, tracks_data, metadata):
    """Export organized regions to new AAF file"""
    try:
        # Create output directory if it doesn't exist
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create new AAF file
        with aaf2.open(output_file, 'w') as f:
            # Set composition metadata
            comp_name = metadata.get('composition_name', 'WAAFer Export')
            frame_rate = metadata.get('frame_rate', 25.0)
            
            # Create composition mob
            comp_mob = f.create.CompositionMob(comp_name)
            f.content.mobs.append(comp_mob)
            
            # Create timeline slots for each track
            for track_data in tracks_data:
                track_name = track_data.get('name', 'Track')
                track_index = track_data.get('index', 0)
                
                # Create timeline mob slot
                slot = comp_mob.create_timeline_slot(edit_rate=frame_rate)
                slot.slot_name = track_name
                slot.slot_id = track_index + 1
                
                # Create sequence for this track
                sequence = f.create.Sequence(media_kind='sound')
                slot.segment = sequence
                
                # Add regions assigned to this track
                track_regions = [r for r in regions_data if r.get('assigned_track_index') == track_index]
                
                for region in track_regions:
                    # Create source clip for each region
                    source_file = region.get('audio_file_path', '')
                    start_time = region.get('start_time', 0.0)
                    duration = region.get('duration', 1.0)
                    
                    # Convert time to frames
                    start_frames = int(start_time * frame_rate)
                    duration_frames = int(duration * frame_rate)
                    
                    if source_file and os.path.exists(source_file):
                        # Create source mob for audio file
                        source_mob = f.create.SourceMob()
                        source_mob.name = os.path.basename(source_file)
                        f.content.mobs.append(source_mob)
                        
                        # Create source clip
                        source_clip = f.create.SourceClip()
                        source_clip.length = duration_frames
                        source_clip.start_time = start_frames
                        source_clip.source_mob = source_mob
                        
                        # Set region name
                        source_clip.name = region.get('name', f'Region_{len(sequence.components)}')
                        
                        sequence.components.append(source_clip)
                    else:
                        # Create filler for missing audio
                        filler = f.create.Filler()
                        filler.length = duration_frames
                        sequence.components.append(filler)
        
        return True, f"AAF export completed: {output_file}"
        
    except Exception as e:
        return False, f"AAF export failed: {str(e)}"

# Execute export
source_file = r"%1"
output_file = r"%2"
regions_data = %3
tracks_data = %4
metadata = %5

success, message = export_aaf_file(source_file, output_file, regions_data, tracks_data, metadata)
print("EXPORT_RESULT:", success, "|", message)
)").arg(m_aafReader->currentFile())
   .arg(m_currentOutputPath)
   .arg(QString::fromUtf8(QJsonDocument::fromVariant(exportRegions).toJson(QJsonDocument::Compact)))
   .arg(QString::fromUtf8(QJsonDocument::fromVariant(m_currentTracks).toJson(QJsonDocument::Compact)))
   .arg(QString::fromUtf8(QJsonDocument::fromVariant(metadata).toJson(QJsonDocument::Compact)));
    
    // Execute Python script
    qDebug() << "AAF Export: Executing Python script for AAF export";
    QString result = m_pythonBridge->executePythonCommand(pythonScript);
    qDebug() << "AAF Export: Python script result:" << result;

    // Check for library import errors
    if (result.contains("EXPORT_ERROR: Missing AAF library")) {
        QString error = "AAF library (pyaaf2) not installed. Please install with: pip install pyaaf2";
        qCritical() << "AAF Export Error:" << error;
        emit exportFailed(error);
        return false;
    }

    if (result.contains("EXPORT_RESULT: True")) {
        qDebug() << "AAF export successful";
        return true;
    } else {
        QString error = result.contains("EXPORT_RESULT: False") ?
                       result.split("|").last().trimmed() :
                       QString("AAF export failed. Result: %1").arg(result);
        qCritical() << "AAF Export Error:" << error;
        emit exportFailed(error);
        return false;
    }
}

bool AAFExporter::exportToOMF()
{
    if (!m_pythonBridge || !m_pythonBridge->isInitialized()) {
        emit exportFailed("Python bridge not available for OMF export");
        return false;
    }

    if (!m_aafReader) {
        emit exportFailed("AAF reader not available");
        return false;
    }

    // Prepare regions for export
    QVariantList exportRegions = prepareRegionsForExport();
    QVariantMap metadata = generateExportMetadata();

    // Create Python script for OMF export
    QString pythonScript = QString(R"(
import os
import struct
from pathlib import Path

def export_omf_file(source_file, output_file, regions_data, tracks_data, metadata):
    """Export organized regions to OMF file"""
    try:
        # Create output directory if it doesn't exist
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # OMF is a complex binary format, this is a simplified implementation
        # For production use, consider using a dedicated OMF library

        # Create basic OMF structure
        with open(output_file, 'wb') as f:
            # OMF Header
            f.write(b'OMFI')  # OMF Interchange signature
            f.write(struct.pack('<I', 2))  # Version 2
            f.write(struct.pack('<I', len(regions_data)))  # Number of objects

            # Write composition header
            comp_name = metadata.get('composition_name', 'WAAFer Export').encode('utf-8')[:63]
            f.write(struct.pack('<I', len(comp_name)))
            f.write(comp_name)

            # Write track information
            f.write(struct.pack('<I', len(tracks_data)))
            for track_data in tracks_data:
                track_name = track_data.get('name', 'Track').encode('utf-8')[:63]
                f.write(struct.pack('<I', len(track_name)))
                f.write(track_name)
                f.write(struct.pack('<I', track_data.get('index', 0)))

            # Write region information
            for region in regions_data:
                region_name = region.get('name', 'Region').encode('utf-8')[:63]
                f.write(struct.pack('<I', len(region_name)))
                f.write(region_name)

                # Time information (in samples at 48kHz)
                start_samples = int(region.get('start_time', 0.0) * 48000)
                duration_samples = int(region.get('duration', 1.0) * 48000)
                f.write(struct.pack('<Q', start_samples))
                f.write(struct.pack('<Q', duration_samples))

                # Audio file reference
                audio_file = region.get('audio_file_path', '').encode('utf-8')[:255]
                f.write(struct.pack('<I', len(audio_file)))
                f.write(audio_file)

        return True, f"OMF export completed: {output_file}"

    except Exception as e:
        return False, f"OMF export failed: {str(e)}"

# Execute export
source_file = r"%1"
output_file = r"%2"
regions_data = %3
tracks_data = %4
metadata = %5

success, message = export_omf_file(source_file, output_file, regions_data, tracks_data, metadata)
print("EXPORT_RESULT:", success, "|", message)
)").arg(m_aafReader->currentFile())
   .arg(m_currentOutputPath)
   .arg(QString::fromUtf8(QJsonDocument::fromVariant(exportRegions).toJson(QJsonDocument::Compact)))
   .arg(QString::fromUtf8(QJsonDocument::fromVariant(m_currentTracks).toJson(QJsonDocument::Compact)))
   .arg(QString::fromUtf8(QJsonDocument::fromVariant(metadata).toJson(QJsonDocument::Compact)));

    // Execute Python script
    QString result = m_pythonBridge->executePythonCommand(pythonScript);

    if (result.contains("EXPORT_RESULT: True")) {
        qDebug() << "OMF export successful";
        return true;
    } else {
        QString error = result.contains("EXPORT_RESULT: False") ?
                       result.split("|").last().trimmed() : "Unknown OMF export error";
        emit exportFailed(error);
        return false;
    }
}

bool AAFExporter::exportToXML()
{
    // Prepare regions for export
    QVariantList exportRegions = prepareRegionsForExport();
    QVariantMap metadata = generateExportMetadata();
    
    // Generate Final Cut Pro X compatible XML
    QString xmlContent = QString(R"(<?xml version="1.0" encoding="UTF-8"?>
<fcpxml version="1.8">
    <resources>
        <format id="r1" name="FFVideoFormat1080p25" frameDuration="1/25s" width="1920" height="1080"/>
    </resources>
    <library>
        <event name="%1">
            <project name="%2">
                <sequence format="r1" tcStart="0s" tcFormat="NDF" audioLayout="stereo" audioRate="48k">
                    <spine>
)").arg(metadata.value("project_name", "WAAFer Export").toString())
   .arg(metadata.value("composition_name", "Organized Audio").toString());
    
    // Add audio clips for each region
    for (const QVariant &regionVariant : exportRegions) {
        QVariantMap region = regionVariant.toMap();
        
        QString clipName = region.value("name").toString();
        double startTime = region.value("start_time").toDouble();
        double duration = region.value("duration").toDouble();
        QString audioFile = region.value("audio_file_path").toString();
        
        xmlContent += QString(R"(
                        <audio name="%1" offset="%2s" duration="%3s" start="0s">
                            <audio-role>dialogue</audio-role>
                        </audio>)")
                     .arg(clipName)
                     .arg(startTime, 0, 'f', 3)
                     .arg(duration, 0, 'f', 3);
    }
    
    xmlContent += R"(
                    </spine>
                </sequence>
            </project>
        </event>
    </library>
</fcpxml>)";
    
    // Write XML file
    QFile file(m_currentOutputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportFailed(QString("Cannot write XML file: %1").arg(file.errorString()));
        return false;
    }
    
    QTextStream out(&file);
    out << xmlContent;
    file.close();
    
    qDebug() << "XML export successful";
    return true;
}

bool AAFExporter::exportToCSV()
{
    // Prepare regions for export
    QVariantList exportRegions = prepareRegionsForExport();

    // Create CSV content
    QStringList csvLines;
    csvLines << "Region Name,Track,Content Type,Speaker,Start Time,Duration,Confidence,Audio File";

    for (const QVariant &regionVariant : exportRegions) {
        QVariantMap region = regionVariant.toMap();

        QStringList fields;
        fields << QString("\"%1\"").arg(region.value("name").toString());
        fields << QString("\"%1\"").arg(region.value("track").toString());
        fields << QString("\"%1\"").arg(region.value("content_type").toString());
        fields << QString("\"%1\"").arg(region.value("speaker_id").toString());
        fields << formatTimecodeForExport(region.value("start_time").toDouble());
        fields << QString::number(region.value("duration").toDouble(), 'f', 3);
        fields << QString::number(region.value("confidence").toDouble(), 'f', 3);
        fields << QString("\"%1\"").arg(region.value("audio_file_path").toString());

        csvLines << fields.join(",");
    }

    // Write CSV file
    QFile file(m_currentOutputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportFailed(QString("Cannot write CSV file: %1").arg(file.errorString()));
        return false;
    }

    QTextStream out(&file);
    for (const QString &line : csvLines) {
        out << line << "\n";
    }
    file.close();

    qDebug() << "CSV export successful";
    return true;
}

bool AAFExporter::exportToJSON()
{
    // Prepare regions for export
    QVariantList exportRegions = prepareRegionsForExport();
    QVariantMap metadata = generateExportMetadata();

    // Create JSON structure
    QJsonObject jsonRoot;
    jsonRoot["metadata"] = QJsonObject::fromVariantMap(metadata);
    jsonRoot["tracks"] = QJsonArray::fromVariantList(m_currentTracks);
    jsonRoot["regions"] = QJsonArray::fromVariantList(exportRegions);

    // Write JSON file
    QFile file(m_currentOutputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportFailed(QString("Cannot write JSON file: %1").arg(file.errorString()));
        return false;
    }

    QJsonDocument doc(jsonRoot);
    file.write(doc.toJson());
    file.close();

    qDebug() << "JSON export successful";
    return true;
}

bool AAFExporter::validateExportParameters()
{
    if (m_currentRegions.isEmpty()) {
        emit exportFailed("No regions to export");
        return false;
    }

    if (m_currentOutputPath.isEmpty()) {
        emit exportFailed("No output path specified");
        return false;
    }

    // Check if output directory exists and is writable
    QFileInfo outputInfo(m_currentOutputPath);
    QDir outputDir = outputInfo.dir();

    if (!outputDir.exists()) {
        if (!outputDir.mkpath(".")) {
            emit exportFailed("Cannot create output directory");
            return false;
        }
    }

    // For AAF export, check if Python bridge and AAF reader are available
    if (m_currentOptions.format == AAF) {
        if (!m_pythonBridge || !m_pythonBridge->isInitialized()) {
            emit exportFailed("Python bridge required for AAF export");
            return false;
        }

        if (!m_aafReader) {
            emit exportFailed("AAF reader required for AAF export");
            return false;
        }
    }

    return true;
}

QVariantList AAFExporter::prepareRegionsForExport()
{
    QVariantList exportRegions;

    for (const QVariant &regionVariant : m_currentRegions) {
        QVariantMap region = regionVariant.toMap();
        QVariantMap exportRegion;

        // Copy essential fields with null safety
        exportRegion["id"] = region.value("id", QString(""));
        exportRegion["name"] = region.value("name", QString("Unknown Region"));
        exportRegion["track"] = region.value("track", QString(""));
        exportRegion["track_name"] = region.value("trackName", region.value("track", QString("Main Audio Track")));
        exportRegion["start_time"] = region.value("startTime", 0.0);
        exportRegion["duration"] = region.value("duration", 0.0);

        // Get audio file path from multiple possible sources
        QString audioFilePath = region.value("audioFilePath", QString("")).toString();
        if (audioFilePath.isEmpty()) {
            audioFilePath = region.value("audio_file_path", QString("")).toString();
        }
        if (audioFilePath.isEmpty()) {
            audioFilePath = region.value("sourceFile", QString("")).toString();
        }
        if (audioFilePath.isEmpty() && m_aafReader) {
            // Try to get from AAF reader based on region
            audioFilePath = m_aafReader->getAudioFileForRegion(region.value("id", QString("")).toString());
        }
        exportRegion["audio_file_path"] = audioFilePath;

        // Add source timing information
        exportRegion["source_start_time"] = region.value("sourceStartTime", region.value("start_time", 0.0));

        // Add LibAAF-compatible fields with proper sample rate
        double sampleRate = 48000.0; // Default sample rate
        QString sampleRateStr = m_currentOptions.sampleRate;
        if (!sampleRateStr.isEmpty()) {
            // Extract numeric value from string like "48000 Hz"
            QRegularExpression re("(\\d+)");
            QRegularExpressionMatch match = re.match(sampleRateStr);
            if (match.hasMatch()) {
                sampleRate = match.captured(1).toDouble();
            }
        }
        exportRegion["lengthEditUnits"] = static_cast<qint64>(region.value("duration", 0.0).toDouble() * sampleRate);
        exportRegion["essenceOffsetEditUnits"] = static_cast<qint64>(region.value("startTime", 0.0).toDouble() * sampleRate);

        // Add classification data if included
        if (m_currentOptions.includeClassification) {
            exportRegion["content_type"] = region.value("contentType", QString("Unknown"));
            exportRegion["speaker_id"] = region.value("speakerId", QString(""));
            exportRegion["confidence"] = region.value("confidence", 0.0);
        }

        // Add organization data if included
        if (m_currentOptions.includeOrganization) {
            exportRegion["assigned_track"] = region.value("assignedTrack", QString(""));
            exportRegion["assigned_track_index"] = region.value("assignedTrackIndex", 0);
        }

        // Add metadata if included
        if (m_currentOptions.includeMetadata) {
            QVariant metadataVar = region.value("metadata");
            if (metadataVar.isValid() && !metadataVar.isNull()) {
                exportRegion["metadata"] = metadataVar;
            } else {
                exportRegion["metadata"] = QVariantMap(); // Empty map instead of null
            }
        }

        exportRegions.append(exportRegion);
    }

    return exportRegions;
}

QVariantMap AAFExporter::generateExportMetadata()
{
    QVariantMap metadata;

    // Basic export information
    metadata["export_timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    metadata["export_format"] = static_cast<int>(m_currentOptions.format);
    metadata["exporter_version"] = "WAAFer 1.0.0";

    // Source file information
    if (m_aafReader) {
        metadata["source_file"] = m_aafReader->currentFile();
        metadata["frame_rate"] = m_aafReader->frameRate();
        metadata["duration"] = m_aafReader->duration();
        metadata["composition_name"] = QFileInfo(m_aafReader->currentFile()).baseName();
    } else {
        metadata["composition_name"] = "WAAFer Export";
        metadata["frame_rate"] = 25.0; // Default frame rate
    }

    // Audio format information
    int sampleRate = 48000; // Default sample rate
    QString sampleRateStr = m_currentOptions.sampleRate;
    if (!sampleRateStr.isEmpty()) {
        // Extract numeric value from string like "48000 Hz"
        QRegularExpression re("(\\d+)");
        QRegularExpressionMatch match = re.match(sampleRateStr);
        if (match.hasMatch()) {
            sampleRate = match.captured(1).toInt();
        }
    }

    int bitDepth = 16; // Default bit depth
    QString bitDepthStr = m_currentOptions.bitDepth;
    if (!bitDepthStr.isEmpty()) {
        // Extract numeric value from string like "24-bit"
        QRegularExpression re("(\\d+)");
        QRegularExpressionMatch match = re.match(bitDepthStr);
        if (match.hasMatch()) {
            bitDepth = match.captured(1).toInt();
        }
    }

    metadata["sample_rate"] = sampleRate;
    metadata["bit_depth"] = bitDepth;
    metadata["channels"] = 2; // Stereo by default

    // Project information
    metadata["project_name"] = "WAAFer Export";
    metadata["total_regions"] = m_currentRegions.size();
    metadata["total_tracks"] = m_currentTracks.size();

    // Export options
    QVariantMap options;
    options["include_audio"] = m_currentOptions.includeAudio;
    options["include_metadata"] = m_currentOptions.includeMetadata;
    options["include_timecode"] = m_currentOptions.includeTimecode;
    options["include_classification"] = m_currentOptions.includeClassification;
    options["include_organization"] = m_currentOptions.includeOrganization;
    options["audio_quality"] = m_currentOptions.audioQuality;
    options["sample_rate"] = m_currentOptions.sampleRate;
    options["bit_depth"] = m_currentOptions.bitDepth;
    metadata["export_options"] = options;

    return metadata;
}

void AAFExporter::setProgress(double progress, const QString &message)
{
    m_progress = progress;
    m_statusMessage = message;
    emit progressChanged(progress, message);
}

void AAFExporter::resetState()
{
    m_isExporting = false;
    m_progress = 0.0;
    m_statusMessage.clear();
    m_currentStep = 0;
    m_totalSteps = 0;
    m_progressTimer->stop();
}

QString AAFExporter::formatTimecodeForExport(double timeInSeconds) const
{
    int hours = static_cast<int>(timeInSeconds) / 3600;
    int minutes = (static_cast<int>(timeInSeconds) % 3600) / 60;
    int seconds = static_cast<int>(timeInSeconds) % 60;
    int milliseconds = static_cast<int>((timeInSeconds - static_cast<int>(timeInSeconds)) * 1000);

    return QString("%1:%2:%3.%4")
           .arg(hours, 2, 10, QChar('0'))
           .arg(minutes, 2, 10, QChar('0'))
           .arg(seconds, 2, 10, QChar('0'))
           .arg(milliseconds, 3, 10, QChar('0'));
}
