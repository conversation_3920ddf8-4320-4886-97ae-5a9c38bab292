#include "TimelineWidget.h"
#include "../core/AAFReader.h"
#include "../audio/AudioFileManager.h"
#include "../audio/AudioPlaybackManager.h"
#include "../utils/TimecodeUtils.h"

#include <QApplication>
#include <QSplitter>
#include <QScrollArea>
#include <QScrollBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QLabel>
#include <QPushButton>
#include <QToolButton>
#include <QSlider>
#include <QTimer>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QMenu>
#include <QAction>
#include <QListWidget>
#include <QGroupBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QContextMenuEvent>
#include <QToolBar>
#include <QFrame>
#include <QButtonGroup>
#include <QDebug>

// TimelineRuler Implementation
TimelineRuler::TimelineRuler(QWidget *parent)
    : QWidget(parent)
{
    setFixedHeight(m_rulerHeight);
    setMouseTracking(true);
    setMinimumWidth(800);
}

void TimelineRuler::setDuration(double duration)
{
    m_duration = duration;
    update();
}

void TimelineRuler::setZoom(double zoom)
{
    m_zoom = qMax(0.1, qMin(10.0, zoom)); // Clamp zoom between 0.1x and 10x
    update();
}

void TimelineRuler::setPlayheadPosition(double position)
{
    m_playheadPosition = position;
    update();
}

void TimelineRuler::setFrameRate(double frameRate)
{
    m_frameRate = frameRate;
    update();
}

void TimelineRuler::setTimecodeFormat(int format)
{
    m_timecodeFormat = format;
    update();
}

double TimelineRuler::pixelsToTime(int pixels) const
{
    return (pixels / m_zoom) / 100.0; // 100 pixels per second at zoom 1.0
}

int TimelineRuler::timeToPixels(double time) const
{
    return static_cast<int>(time * 100.0 * m_zoom);
}

QString TimelineRuler::formatTimecode(double seconds) const
{
    if (m_timecodeFormat == 0) {
        // Frame-based timecode (HH:MM:SS:FF)
        TimecodeUtils::FrameRate frameRate = TimecodeUtils::FrameRate::FPS_25;
        if (qAbs(m_frameRate - 23.976) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_23_976;
        else if (qAbs(m_frameRate - 24.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_24;
        else if (qAbs(m_frameRate - 25.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_25;
        else if (qAbs(m_frameRate - 29.97) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_29_97;
        else if (qAbs(m_frameRate - 30.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_30;

        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::FRAMES, frameRate);
    } else {
        // Millisecond-based timecode (HH:MM:SS.mmm)
        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::MILLISECONDS, TimecodeUtils::FrameRate::FPS_25);
    }
}

void TimelineRuler::drawTimeMarkers(QPainter &painter)
{
    painter.setPen(QColor(180, 180, 180));
    QFont font = painter.font();
    font.setPointSize(9);
    font.setFamily("Monaco, Consolas, monospace");
    painter.setFont(font);

    int width = this->width();

    // Adaptive time step based on zoom level
    double timeStep = 1.0; // 1 second intervals
    if (m_zoom > 4.0) timeStep = 0.1;      // 100ms at high zoom
    else if (m_zoom > 2.0) timeStep = 0.5;  // 500ms at medium zoom
    else if (m_zoom < 0.5) timeStep = 5.0;  // 5s at low zoom
    else if (m_zoom < 0.2) timeStep = 10.0; // 10s at very low zoom

    int labelInterval = (m_zoom > 2.0) ? 10 : 5; // Show fewer labels at high zoom

    for (double time = 0; time <= m_duration; time += timeStep) {
        int x = timeToPixels(time);
        if (x > width) break;

        // Draw tick marks
        int tickHeight = (static_cast<int>(time / timeStep) % labelInterval == 0) ? 15 : 8;
        painter.drawLine(x, m_rulerHeight - tickHeight, x, m_rulerHeight);

        // Draw time labels at major intervals
        if (static_cast<int>(time / timeStep) % labelInterval == 0) {
            QString timeStr = formatTimecode(time);
            QRect textRect = painter.fontMetrics().boundingRect(timeStr);
            painter.drawText(x - textRect.width() / 2, 12, timeStr);
        }
    }
}

void TimelineRuler::drawPlayhead(QPainter &painter)
{
    int playheadX = timeToPixels(m_playheadPosition);
    if (playheadX >= 0 && playheadX <= width()) {
        painter.setPen(QPen(QColor(255, 100, 100), 2));
        painter.drawLine(playheadX, 0, playheadX, height());

        // Draw playhead triangle
        QPolygon triangle;
        triangle << QPoint(playheadX - 5, 0) << QPoint(playheadX + 5, 0) << QPoint(playheadX, 8);
        painter.setBrush(QColor(255, 100, 100));
        painter.drawPolygon(triangle);
    }
}

void TimelineRuler::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Background
    painter.fillRect(rect(), QColor(45, 45, 45));

    // Draw time markers
    drawTimeMarkers(painter);

    // Draw playhead
    drawPlayhead(painter);
}

void TimelineRuler::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        double newPosition = pixelsToTime(static_cast<int>(event->position().x()));
        newPosition = qMax(0.0, qMin(newPosition, m_duration));
        m_playheadPosition = newPosition;
        emit playheadMoved(newPosition);
        update();
    }
}

void TimelineRuler::wheelEvent(QWheelEvent *event)
{
    // Zoom with mouse wheel
    double factor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
    emit zoomRequested(factor);
    event->accept();
}

void TimelineRuler::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // Ensure ruler updates properly on resize
    update();
}

// TimelineToolbar Implementation
TimelineToolbar::TimelineToolbar(QWidget *parent)
    : QToolBar(parent)
{
    setFixedHeight(TimelineWidget::TOOLBAR_HEIGHT);
    setMovable(false);
    setupActions();
}

void TimelineToolbar::setupActions()
{
    // Zoom controls
    m_zoomInAction = addAction("🔍+", this, &TimelineToolbar::onZoomIn);
    m_zoomInAction->setToolTip("Zoom In");

    m_zoomOutAction = addAction("🔍-", this, &TimelineToolbar::onZoomOut);
    m_zoomOutAction->setToolTip("Zoom Out");

    m_fitToWindowAction = addAction("⤢", this, &TimelineToolbar::onFitToWindow);
    m_fitToWindowAction->setToolTip("Fit to Window");

    addSeparator();

    // Display options
    m_showWaveformsAction = addAction("〰️", this, [this]() { emit showWaveformsToggled(m_showWaveformsAction->isChecked()); });
    m_showWaveformsAction->setCheckable(true);
    m_showWaveformsAction->setChecked(true);
    m_showWaveformsAction->setToolTip("Show Waveforms");

    m_showRegionNamesAction = addAction("📝", this, [this]() { emit showRegionNamesToggled(m_showRegionNamesAction->isChecked()); });
    m_showRegionNamesAction->setCheckable(true);
    m_showRegionNamesAction->setChecked(true);
    m_showRegionNamesAction->setToolTip("Show Region Names");

    m_toggleRegionNameSourceAction = addAction("🔄", this, &TimelineToolbar::onToggleRegionNameSource);
    m_toggleRegionNameSourceAction->setCheckable(true);
    m_toggleRegionNameSourceAction->setChecked(false);
    m_toggleRegionNameSourceAction->setToolTip("Toggle Region Name Source (A/B)");

    m_showConfidenceAction = addAction("📊", this, [this]() { emit showConfidenceToggled(m_showConfidenceAction->isChecked()); });
    m_showConfidenceAction->setCheckable(true);
    m_showConfidenceAction->setChecked(true);
    m_showConfidenceAction->setToolTip("Show Confidence Scores");

    addSeparator();

    // Track selection
    m_selectAllTracksAction = addAction("☑️", this, &TimelineToolbar::onSelectAllTracks);
    m_selectAllTracksAction->setToolTip("Select All Tracks");

    m_deselectAllTracksAction = addAction("☐", this, &TimelineToolbar::onDeselectAllTracks);
    m_deselectAllTracksAction->setToolTip("Deselect All Tracks");
}

void TimelineToolbar::onZoomIn() { emit zoomInRequested(); }
void TimelineToolbar::onZoomOut() { emit zoomOutRequested(); }
void TimelineToolbar::onFitToWindow() { emit fitToWindowRequested(); }
void TimelineToolbar::onSelectAllTracks() { emit trackSelectionRequested(true); }
void TimelineToolbar::onDeselectAllTracks() { emit trackSelectionRequested(false); }
void TimelineToolbar::onToggleRegionNameSource() {
    // This will be handled by the parent TimelineWidget
    if (auto timeline = qobject_cast<TimelineWidget*>(parent())) {
        timeline->onToggleRegionNameSource();
    }
}

// TransportControls Implementation
TransportControls::TransportControls(QWidget *parent)
    : QWidget(parent)
{
    setFixedHeight(TimelineWidget::TRANSPORT_HEIGHT);
    setupLayout();
}

void TransportControls::setupLayout()
{
    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->setContentsMargins(8, 4, 8, 4);
    layout->setSpacing(8);

    // Transport buttons
    m_playButton = new QToolButton;
    m_playButton->setText("▶");
    m_playButton->setFixedSize(36, 32);
    m_playButton->setToolTip("Play");

    m_pauseButton = new QToolButton;
    m_pauseButton->setText("⏸");
    m_pauseButton->setFixedSize(36, 32);
    m_pauseButton->setToolTip("Pause");

    m_stopButton = new QToolButton;
    m_stopButton->setText("⏹");
    m_stopButton->setFixedSize(36, 32);
    m_stopButton->setToolTip("Stop");

    layout->addWidget(m_playButton);
    layout->addWidget(m_pauseButton);
    layout->addWidget(m_stopButton);

    layout->addWidget(new QFrame); // Spacer

    // Time display
    m_timeLabel = new QLabel("00:00:00:00");
    m_timeLabel->setStyleSheet("QLabel { font-family: 'Monaco', 'Consolas', monospace; font-size: 12px; }");
    m_timeLabel->setMinimumWidth(80);
    layout->addWidget(m_timeLabel);

    // Position slider
    m_positionSlider = new QSlider(Qt::Horizontal);
    m_positionSlider->setRange(0, 1000);
    layout->addWidget(m_positionSlider, 1);

    // Duration display
    m_durationLabel = new QLabel("/ 00:00:00:00");
    m_durationLabel->setStyleSheet("QLabel { font-family: 'Monaco', 'Consolas', monospace; font-size: 12px; }");
    m_durationLabel->setMinimumWidth(100);
    layout->addWidget(m_durationLabel);

    layout->addWidget(new QFrame); // Spacer

    // Frame rate combo
    layout->addWidget(new QLabel("FPS:"));
    m_frameRateCombo = new QComboBox;
    m_frameRateCombo->addItems({"23.976", "24", "25", "29.97", "30"});
    m_frameRateCombo->setCurrentText("25");
    m_frameRateCombo->setFixedWidth(70);
    layout->addWidget(m_frameRateCombo);

    // Timecode format combo
    layout->addWidget(new QLabel("Format:"));
    m_timecodeFormatCombo = new QComboBox;
    m_timecodeFormatCombo->addItems({"HH:MM:SS:FF", "HH:MM:SS.mmm"});
    m_timecodeFormatCombo->setFixedWidth(100);
    layout->addWidget(m_timecodeFormatCombo);

    // Connect signals
    connect(m_playButton, &QToolButton::clicked, this, &TransportControls::onPlayClicked);
    connect(m_pauseButton, &QToolButton::clicked, this, &TransportControls::onPauseClicked);
    connect(m_stopButton, &QToolButton::clicked, this, &TransportControls::onStopClicked);
    connect(m_positionSlider, &QSlider::valueChanged, this, &TransportControls::onPositionSliderChanged);
    connect(m_frameRateCombo, &QComboBox::currentTextChanged, this, &TransportControls::onFrameRateChanged);
    connect(m_timecodeFormatCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TransportControls::onTimecodeFormatChanged);
}

void TransportControls::setDuration(double duration)
{
    m_duration = duration;
    updateTimeDisplay();
}

void TransportControls::setPosition(double position)
{
    m_position = position;
    updateTimeDisplay();

    if (m_duration > 0) {
        int sliderValue = static_cast<int>((position / m_duration) * 1000);
        m_positionSlider->blockSignals(true);
        m_positionSlider->setValue(sliderValue);
        m_positionSlider->blockSignals(false);
    }
}

void TransportControls::setFrameRate(double frameRate)
{
    m_frameRate = frameRate;
    m_frameRateCombo->blockSignals(true);
    m_frameRateCombo->setCurrentText(QString::number(frameRate));
    m_frameRateCombo->blockSignals(false);
    updateTimeDisplay();
}

void TransportControls::setTimecodeFormat(int format)
{
    m_timecodeFormat = format;
    m_timecodeFormatCombo->blockSignals(true);
    m_timecodeFormatCombo->setCurrentIndex(format);
    m_timecodeFormatCombo->blockSignals(false);
    updateTimeDisplay();
}

void TransportControls::updateTimeDisplay()
{
    m_timeLabel->setText(formatTimecode(m_position));
    m_durationLabel->setText("/ " + formatTimecode(m_duration));
}

QString TransportControls::formatTimecode(double seconds) const
{
    if (m_timecodeFormat == 0) {
        // Frame-based timecode (HH:MM:SS:FF)
        TimecodeUtils::FrameRate frameRate = TimecodeUtils::FrameRate::FPS_25;
        if (qAbs(m_frameRate - 23.976) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_23_976;
        else if (qAbs(m_frameRate - 24.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_24;
        else if (qAbs(m_frameRate - 25.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_25;
        else if (qAbs(m_frameRate - 29.97) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_29_97;
        else if (qAbs(m_frameRate - 30.0) < 0.1) frameRate = TimecodeUtils::FrameRate::FPS_30;

        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::FRAMES, frameRate);
    } else {
        // Millisecond-based timecode (HH:MM:SS.mmm)
        return TimecodeUtils::formatTimecode(seconds, TimecodeUtils::Format::MILLISECONDS, TimecodeUtils::FrameRate::FPS_25);
    }
}

void TransportControls::play()
{
    m_playing = true;
    m_playButton->setStyleSheet("QToolButton { background-color: #4CAF50; }");
    m_pauseButton->setStyleSheet("");
}

void TransportControls::pause()
{
    m_playing = false;
    m_playButton->setStyleSheet("");
    m_pauseButton->setStyleSheet("QToolButton { background-color: #FF9800; }");
}

void TransportControls::stop()
{
    m_playing = false;
    m_position = 0.0;
    setPosition(0.0);
    m_playButton->setStyleSheet("");
    m_pauseButton->setStyleSheet("");
}

void TransportControls::setPlayheadPosition(double position)
{
    setPosition(position);
}

void TransportControls::onPlayClicked()
{
    emit playRequested();
}

void TransportControls::onPauseClicked()
{
    emit pauseRequested();
}

void TransportControls::onStopClicked()
{
    emit stopRequested();
}

void TransportControls::onPositionSliderChanged(int value)
{
    if (m_duration > 0) {
        double newPosition = (value / 1000.0) * m_duration;
        emit positionChanged(newPosition);
    }
}

void TransportControls::onFrameRateChanged()
{
    bool ok;
    double frameRate = m_frameRateCombo->currentText().toDouble(&ok);
    if (ok) {
        m_frameRate = frameRate;
        updateTimeDisplay();
        emit frameRateChanged(frameRate);
    }
}

void TransportControls::onTimecodeFormatChanged()
{
    m_timecodeFormat = m_timecodeFormatCombo->currentIndex();
    updateTimeDisplay();
    emit timecodeFormatChanged(m_timecodeFormat);
}



// TrackHeader Implementation
TrackHeader::TrackHeader(const TimelineTrack &track, QWidget *parent)
    : QWidget(parent), m_track(track)
{
    setFixedWidth(HEADER_WIDTH);
    setMinimumHeight(TimelineWidget::TRACK_HEIGHT);
    setupLayout();
}

void TrackHeader::setupLayout()
{
    QGridLayout *layout = new QGridLayout(this);
    layout->setContentsMargins(8, 4, 8, 4);
    layout->setSpacing(4);

    // Track number label
    m_trackNumberLabel = new QLabel(QString::number(m_track.trackNumber >= 0 ? m_track.trackNumber + 1 : 1));
    m_trackNumberLabel->setStyleSheet("QLabel { color: #888; font-weight: bold; font-size: 10px; }");
    m_trackNumberLabel->setFixedWidth(20);
    layout->addWidget(m_trackNumberLabel, 0, 0);

    // Selection checkbox
    m_selectionCheckBox = new QCheckBox;
    m_selectionCheckBox->setChecked(m_track.selected);
    m_selectionCheckBox->setToolTip("Select track for analysis/export");
    connect(m_selectionCheckBox, &QCheckBox::toggled, this, &TrackHeader::onSelectionClicked);
    layout->addWidget(m_selectionCheckBox, 0, 1);

    // Track name (editable)
    m_nameEdit = new QLineEdit(m_track.name);
    m_nameEdit->setStyleSheet("QLineEdit { background: transparent; border: 1px solid #555; color: white; padding: 2px; }");
    m_nameEdit->setToolTip("Double-click to edit track name");
    connect(m_nameEdit, &QLineEdit::editingFinished, this, &TrackHeader::onNameEditFinished);
    layout->addWidget(m_nameEdit, 0, 2, 1, 4);

    // Control buttons row
    m_muteButton = new QToolButton;
    m_muteButton->setText("M");
    m_muteButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_muteButton->setCheckable(true);
    m_muteButton->setChecked(m_track.muted);
    m_muteButton->setToolTip("Mute track");
    connect(m_muteButton, &QToolButton::clicked, this, &TrackHeader::onMuteClicked);
    layout->addWidget(m_muteButton, 1, 2);

    m_soloButton = new QToolButton;
    m_soloButton->setText("S");
    m_soloButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_soloButton->setCheckable(true);
    m_soloButton->setChecked(m_track.solo);
    m_soloButton->setToolTip("Solo track");
    connect(m_soloButton, &QToolButton::clicked, this, &TrackHeader::onSoloClicked);
    layout->addWidget(m_soloButton, 1, 3);

    m_visibilityButton = new QToolButton;
    m_visibilityButton->setText("👁");
    m_visibilityButton->setFixedSize(BUTTON_SIZE, BUTTON_SIZE);
    m_visibilityButton->setCheckable(true);
    m_visibilityButton->setChecked(m_track.visible);
    m_visibilityButton->setToolTip("Show/hide track");
    connect(m_visibilityButton, &QToolButton::clicked, this, &TrackHeader::onVisibilityClicked);
    layout->addWidget(m_visibilityButton, 1, 4);

    updateButtonStates();
}

void TrackHeader::updateButtonStates()
{
    QString muteStyle = m_track.muted ?
        "QToolButton { background-color: #ff6b6b; color: white; }" :
        "QToolButton { background-color: #555; color: #ccc; }";
    m_muteButton->setStyleSheet(muteStyle);

    QString soloStyle = m_track.solo ?
        "QToolButton { background-color: #ffd93d; color: black; }" :
        "QToolButton { background-color: #555; color: #ccc; }";
    m_soloButton->setStyleSheet(soloStyle);

    QString visStyle = m_track.visible ?
        "QToolButton { background-color: #6bcf7f; color: white; }" :
        "QToolButton { background-color: #555; color: #ccc; }";
    m_visibilityButton->setStyleSheet(visStyle);
}

void TrackHeader::updateTrack(const TimelineTrack &track)
{
    m_track = track;
    m_nameEdit->setText(track.name);
    m_muteButton->setChecked(track.muted);
    m_soloButton->setChecked(track.solo);
    m_visibilityButton->setChecked(track.visible);
    m_selectionCheckBox->setChecked(track.selected);
    m_trackNumberLabel->setText(QString::number(track.trackNumber >= 0 ? track.trackNumber + 1 : 1));
    updateButtonStates();
    update();
}

QString TrackHeader::getTrackName() const
{
    return m_nameEdit->text();
}

bool TrackHeader::isSelected() const
{
    return m_selectionCheckBox->isChecked();
}

void TrackHeader::setSelected(bool selected)
{
    m_selectionCheckBox->setChecked(selected);
}

void TrackHeader::setHeight(int height)
{
    setMinimumHeight(height);
    setMaximumHeight(height);
}

void TrackHeader::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);

    // Background with track color
    QColor bgColor = m_track.color.darker(200);
    painter.fillRect(rect(), bgColor);

    // Border
    painter.setPen(QColor(80, 80, 80));
    painter.drawRect(rect().adjusted(0, 0, -1, -1));

    // Selection highlight
    if (m_track.selected) {
        painter.setPen(QPen(QColor(100, 150, 255), 2));
        painter.drawRect(rect().adjusted(1, 1, -2, -2));
    }
}

void TrackHeader::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // Allow clicking on the header to toggle selection
        bool newSelection = !m_selectionCheckBox->isChecked();
        m_selectionCheckBox->setChecked(newSelection);
        emit selectionToggled(m_track.name, newSelection);
    }
    QWidget::mousePressEvent(event);
}

void TrackHeader::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // Ensure header redraws properly on resize
    update();
}

void TrackHeader::onMuteClicked()
{
    m_track.muted = m_muteButton->isChecked();
    updateButtonStates();
    emit muteToggled(m_track.name, m_track.muted);
}

void TrackHeader::onSoloClicked()
{
    m_track.solo = m_soloButton->isChecked();
    updateButtonStates();
    emit soloToggled(m_track.name, m_track.solo);
}

void TrackHeader::onVisibilityClicked()
{
    m_track.visible = m_visibilityButton->isChecked();
    updateButtonStates();
    emit visibilityToggled(m_track.name, m_track.visible);
}

void TrackHeader::onSelectionClicked()
{
    m_track.selected = m_selectionCheckBox->isChecked();
    update(); // Refresh selection highlight
    emit selectionToggled(m_track.name, m_track.selected);
}

void TrackHeader::onNameEditFinished()
{
    QString newName = m_nameEdit->text().trimmed();
    if (!newName.isEmpty() && newName != m_track.name) {
        QString oldName = m_track.name;
        m_track.name = newName;
        emit trackRenamed(oldName, newName);
    }
}

// TrackContent Implementation
TrackContent::TrackContent(const TimelineTrack &track, QWidget *parent)
    : QWidget(parent), m_track(track)
{
    setFixedHeight(TimelineWidget::TRACK_HEIGHT);
    setMinimumWidth(1000);
    setMouseTracking(true);

    // Optimize rendering and prevent visual artifacts
    setAttribute(Qt::WA_OpaquePaintEvent, true);
    setAutoFillBackground(true);
    setContentsMargins(0, 0, 0, 0);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // Additional attributes to prevent scrolling artifacts
    setAttribute(Qt::WA_NoSystemBackground, false);
    setAttribute(Qt::WA_PaintOnScreen, false);
    setAttribute(Qt::WA_StaticContents, false);

    // Set background color
    QPalette trackPalette = palette();
    trackPalette.setColor(QPalette::Window, QColor(35, 35, 35));
    setPalette(trackPalette);
}

void TrackContent::setTrack(const TimelineTrack &track)
{
    m_track = track;
    update();
}

void TrackContent::setZoom(double zoom)
{
    m_zoom = qMax(0.1, qMin(10.0, zoom)); // Clamp zoom
    int newWidth = timeToPixels(m_duration);
    setMinimumWidth(newWidth);
    // Use setFixedWidth for proper scroll area handling
    setFixedWidth(newWidth);
    update();
}

void TrackContent::setDuration(double duration)
{
    m_duration = duration;
    int newWidth = timeToPixels(duration);
    setMinimumWidth(newWidth);
    setFixedWidth(newWidth);
    update();
}

void TrackContent::setPlayheadPosition(double position)
{
    m_playheadPosition = position;
    update();
}

void TrackContent::setTimeRangeHighlight(double startTime, double endTime, bool enabled)
{
    m_timeRangeHighlightEnabled = enabled;
    m_timeRangeStartTime = startTime;
    m_timeRangeEndTime = endTime;
    update();
}

void TrackContent::setTimelineWidget(TimelineWidget *timelineWidget)
{
    m_timelineWidget = timelineWidget;
}

double TrackContent::pixelsToTime(int pixels) const
{
    return (pixels / m_zoom) / 100.0;
}

int TrackContent::timeToPixels(double time) const
{
    return static_cast<int>(time * 100.0 * m_zoom);
}

void TrackContent::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // Enable composition mode for better rendering during scrolling
    painter.setCompositionMode(QPainter::CompositionMode_SourceOver);

    QRect exposedRect = event->rect();
    QRect widgetRect = rect();

    // Ensure we only paint within widget bounds with extra safety margin
    QRect clippedRect = exposedRect.intersected(widgetRect);
    if (clippedRect.isEmpty()) {
        return;
    }

    // Set strict clipping to prevent overflow and artifacts
    painter.setClipRect(clippedRect, Qt::IntersectClip);

    // Save painter state for restoration
    painter.save();

    // Background with proper bounds checking
    painter.fillRect(clippedRect, QColor(35, 35, 35));

    // Track border (only draw if visible)
    painter.setPen(QColor(60, 60, 60));
    if (widgetRect.intersects(clippedRect)) {
        painter.drawRect(widgetRect.adjusted(0, 0, -1, -1));
    }

    // Draw time range highlight if enabled
    if (m_timeRangeHighlightEnabled) {
        drawTimeRangeHighlight(painter);
    }

    // Draw regions with optimized visibility checking
    for (const auto &region : m_track.regions) {
        int regionStartX = timeToPixels(region.startTime);
        int regionWidth = qMax(1, timeToPixels(region.duration));

        // Skip regions completely outside visible area
        if (regionStartX + regionWidth < clippedRect.left() || regionStartX > clippedRect.right()) {
            continue;
        }

        // Only draw regions that intersect with the clipped area
        QRect regionRect(regionStartX, 4, regionWidth, height() - 8);
        if (regionRect.intersects(clippedRect)) {
            drawRegion(painter, region);
        }
    }

    // Draw playhead
    drawPlayhead(painter);

    // Restore painter state
    painter.restore();
}

void TrackContent::drawTimeRangeHighlight(QPainter &painter)
{
    if (!m_timeRangeHighlightEnabled) return;

    int startX = timeToPixels(m_timeRangeStartTime);
    int endX = timeToPixels(m_timeRangeEndTime);

    QRect highlightRect(startX, 0, endX - startX, height());
    painter.fillRect(highlightRect, QColor(100, 150, 255, 40));

    // Draw borders
    painter.setPen(QPen(QColor(100, 150, 255), 2));
    painter.drawLine(startX, 0, startX, height());
    painter.drawLine(endX, 0, endX, height());
}

void TrackContent::drawPlayhead(QPainter &painter)
{
    int playheadX = timeToPixels(m_playheadPosition);
    if (playheadX >= 0 && playheadX <= width()) {
        painter.setPen(QPen(QColor(255, 100, 100), 2));
        painter.drawLine(playheadX, 0, playheadX, height());
    }
}

void TrackContent::drawWaveform(QPainter &painter, const TimelineRegion &region, const QRect &rect)
{
    if (!m_showWaveforms || rect.width() < 20) return;

    // Simple waveform visualization (placeholder for actual audio data)
    painter.setPen(QColor(150, 150, 150, 100));
    int centerY = rect.center().y();

    for (int x = rect.left(); x < rect.right(); x += 2) {
        int amplitude = (rand() % 20) - 10; // Mock waveform data
        painter.drawLine(x, centerY - amplitude, x, centerY + amplitude);
    }
}

void TrackContent::drawRegion(QPainter &painter, const TimelineRegion &region)
{
    int x = timeToPixels(region.startTime);
    int width = qMax(1, timeToPixels(region.duration));
    int y = 4;
    int height = this->height() - 8;

    QRect regionRect(x, y, width, height);

    // Region background with gradient
    QColor bgColor = region.color;
    if (region.selected) {
        bgColor = bgColor.lighter(140);
    }
    if (region.muted) {
        bgColor = bgColor.darker(180);
    }

    // Create gradient for depth
    QLinearGradient gradient(regionRect.topLeft(), regionRect.bottomLeft());
    gradient.setColorAt(0, bgColor.lighter(110));
    gradient.setColorAt(1, bgColor.darker(110));
    painter.fillRect(regionRect, gradient);

    // Draw waveform if enabled and region is wide enough
    if (m_showWaveforms && regionRect.width() > 30) {
        drawWaveform(painter, region, regionRect);
    }

    // Region border
    QPen borderPen;
    if (region.selected) {
        borderPen = QPen(QColor(100, 150, 255), 2);
    } else {
        borderPen = QPen(QColor(120, 120, 120), 1);
    }
    painter.setPen(borderPen);
    painter.drawRect(regionRect);

    // Region text (only if enabled and region is wide enough)
    if (m_showRegionNames && regionRect.width() > 50) {
        painter.setPen(QColor(255, 255, 255));
        QFont font = painter.font();
        font.setPointSize(9);
        font.setBold(region.selected);
        painter.setFont(font);

        // Choose region name based on toggle state
        QString text;
        if (m_timelineWidget && m_timelineWidget->useAlternativeRegionNames() && !region.alternativeName.isEmpty()) {
            text = region.alternativeName;
        } else {
            text = region.name;
        }

        if (!region.speakerId.isEmpty() && regionRect.width() > 100) {
            text += QString(" (%1)").arg(region.speakerId);
        }

        QRect textRect = regionRect.adjusted(4, 2, -4, -2);
        painter.drawText(textRect, Qt::AlignLeft | Qt::AlignTop | Qt::TextWordWrap, text);
    }

    // Confidence indicator (only if enabled)
    if (m_showConfidence && region.confidence > 0 && regionRect.width() > 40) {
        QString confText = QString("%1%").arg(static_cast<int>(region.confidence * 100));
        QColor confColor = region.confidence > 0.8 ? QColor(100, 255, 100) :
                          region.confidence > 0.5 ? QColor(255, 255, 100) : QColor(255, 150, 150);
        painter.setPen(confColor);
        QFont confFont = painter.font();
        confFont.setPointSize(8);
        painter.setFont(confFont);
        painter.drawText(regionRect.adjusted(2, 2, -2, -2), Qt::AlignRight | Qt::AlignBottom, confText);
    }

    // Content type indicator
    if (regionRect.width() > 20) {
        QString typeIndicator;
        if (region.contentType == "Dialogue") typeIndicator = "D";
        else if (region.contentType == "Music") typeIndicator = "M";
        else if (region.contentType == "SFX") typeIndicator = "S";
        else if (region.contentType == "Ambience") typeIndicator = "A";

        if (!typeIndicator.isEmpty()) {
            painter.setPen(QColor(200, 200, 200));
            QFont typeFont = painter.font();
            typeFont.setPointSize(7);
            typeFont.setBold(true);
            painter.setFont(typeFont);
            painter.drawText(regionRect.adjusted(2, 2, -2, -2), Qt::AlignLeft | Qt::AlignBottom, typeIndicator);
        }
    }
}

void TrackContent::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        TimelineRegion *region = getRegionAt(event->position().toPoint());
        if (region) {
            emit regionSelected(region->id);
            m_draggedRegion = region;
            m_dragging = true;
            m_dragStartPos = event->position().toPoint();
            m_dragStartTime = region->startTime;
        }
    }
}

void TrackContent::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && m_draggedRegion) {
        int deltaX = static_cast<int>(event->position().x()) - m_dragStartPos.x();
        double deltaTime = pixelsToTime(deltaX);
        double newStartTime = qMax(0.0, m_dragStartTime + deltaTime);

        m_draggedRegion->startTime = newStartTime;
        update();
    }
}

void TrackContent::mouseReleaseEvent(QMouseEvent *event)
{
    if (m_dragging && m_draggedRegion) {
        emit regionMoved(m_draggedRegion->id, m_draggedRegion->startTime);
        m_dragging = false;
        m_draggedRegion = nullptr;
    }
}

void TrackContent::contextMenuEvent(QContextMenuEvent *event)
{
    TimelineRegion *region = getRegionAt(event->pos());
    if (region) {
        emit regionContextMenu(region->id, event->globalPos());
    }
}

void TrackContent::wheelEvent(QWheelEvent *event)
{
    // Forward zoom requests to timeline
    double factor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
    emit zoomRequested(factor);
    event->accept();
}

void TrackContent::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // Ensure content redraws properly on resize
    update();
}

TimelineRegion* TrackContent::getRegionAt(const QPoint &pos)
{
    for (auto &region : m_track.regions) {
        int x = timeToPixels(region.startTime);
        int width = timeToPixels(region.duration);
        QRect regionRect(x, 5, width, height() - 10);

        if (regionRect.contains(pos)) {
            return &region;
        }
    }
    return nullptr;
}

// RegionMetadataPanel Implementation
RegionMetadataPanel::RegionMetadataPanel(QWidget *parent)
    : QWidget(parent), m_compactMode(false)
{
    setFixedWidth(280);
    setupLayout();
}

void RegionMetadataPanel::setupLayout()
{
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(8, 8, 8, 8);
    layout->setSpacing(6);

    // Basic info (always visible)
    m_basicInfoWidget = new QWidget;
    QFormLayout *basicLayout = new QFormLayout(m_basicInfoWidget);
    basicLayout->setContentsMargins(0, 0, 0, 0);

    m_nameEdit = new QLineEdit;
    m_nameEdit->setPlaceholderText("Region name");

    m_speakerEdit = new QLineEdit;
    m_speakerEdit->setPlaceholderText("Speaker ID");

    m_contentTypeCombo = new QComboBox;
    m_contentTypeCombo->addItems({"Dialogue", "Music", "SFX", "Ambience", "Silence", "Unknown"});

    basicLayout->addRow("Name:", m_nameEdit);
    basicLayout->addRow("Speaker:", m_speakerEdit);
    basicLayout->addRow("Type:", m_contentTypeCombo);

    layout->addWidget(m_basicInfoWidget);

    // Advanced info (collapsible in compact mode)
    m_advancedInfoWidget = new QWidget;
    QVBoxLayout *advancedLayout = new QVBoxLayout(m_advancedInfoWidget);
    advancedLayout->setContentsMargins(0, 0, 0, 0);

    // Timing group
    QGroupBox *timingGroup = new QGroupBox("Timing");
    QFormLayout *timingLayout = new QFormLayout(timingGroup);

    m_startTimeSpin = new QDoubleSpinBox;
    m_startTimeSpin->setRange(0.0, 99999.0);
    m_startTimeSpin->setDecimals(3);
    m_startTimeSpin->setSuffix(" s");

    m_durationSpin = new QDoubleSpinBox;
    m_durationSpin->setRange(0.001, 99999.0);
    m_durationSpin->setDecimals(3);
    m_durationSpin->setSuffix(" s");

    timingLayout->addRow("Start:", m_startTimeSpin);
    timingLayout->addRow("Duration:", m_durationSpin);

    // Analysis group
    QGroupBox *analysisGroup = new QGroupBox("Analysis");
    QFormLayout *analysisLayout = new QFormLayout(analysisGroup);

    m_confidenceSpin = new QDoubleSpinBox;
    m_confidenceSpin->setRange(0.0, 1.0);
    m_confidenceSpin->setDecimals(2);
    m_confidenceSpin->setSuffix("%");
    m_confidenceSpin->setReadOnly(true);

    m_mutedCheck = new QCheckBox;

    analysisLayout->addRow("Confidence:", m_confidenceSpin);
    analysisLayout->addRow("Muted:", m_mutedCheck);

    // Notes
    QGroupBox *notesGroup = new QGroupBox("Notes");
    QVBoxLayout *notesLayout = new QVBoxLayout(notesGroup);

    m_notesEdit = new QTextEdit;
    m_notesEdit->setMaximumHeight(80);
    m_notesEdit->setPlaceholderText("Additional notes...");
    notesLayout->addWidget(m_notesEdit);

    advancedLayout->addWidget(timingGroup);
    advancedLayout->addWidget(analysisGroup);
    advancedLayout->addWidget(notesGroup);

    layout->addWidget(m_advancedInfoWidget);
    layout->addStretch();

    // Connect signals
    connect(m_nameEdit, &QLineEdit::textChanged, this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_speakerEdit, &QLineEdit::textChanged, this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_contentTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_startTimeSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_durationSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_mutedCheck, &QCheckBox::toggled, this, &RegionMetadataPanel::onMetadataChanged);
    connect(m_notesEdit, &QTextEdit::textChanged, this, &RegionMetadataPanel::onMetadataChanged);

    updateLayout();
}

void RegionMetadataPanel::setCompactMode(bool compact)
{
    m_compactMode = compact;
    updateLayout();
}

void RegionMetadataPanel::updateLayout()
{
    if (m_compactMode) {
        setFixedWidth(250);
        m_advancedInfoWidget->setVisible(false);
    } else {
        setFixedWidth(280);
        m_advancedInfoWidget->setVisible(true);
    }
}

void RegionMetadataPanel::setRegion(const TimelineRegion &region)
{
    m_currentRegionId = region.id;

    m_nameEdit->setText(region.name);
    m_speakerEdit->setText(region.speakerId);
    m_contentTypeCombo->setCurrentText(region.contentType);
    m_startTimeSpin->setValue(region.startTime);
    m_durationSpin->setValue(region.duration);
    m_confidenceSpin->setValue(region.confidence * 100);
    m_mutedCheck->setChecked(region.muted);
    m_notesEdit->setPlainText(region.metadata.value("notes").toString());
}

void RegionMetadataPanel::clearRegion()
{
    m_currentRegionId.clear();
    m_nameEdit->clear();
    m_speakerEdit->clear();
    m_contentTypeCombo->setCurrentIndex(0);
    m_startTimeSpin->setValue(0.0);
    m_durationSpin->setValue(0.0);
    m_confidenceSpin->setValue(0.0);
    m_mutedCheck->setChecked(false);
    m_notesEdit->clear();
}

void RegionMetadataPanel::onMetadataChanged()
{
    if (m_currentRegionId.isEmpty()) return;

    QVariantMap metadata;
    metadata["name"] = m_nameEdit->text();
    metadata["speakerId"] = m_speakerEdit->text();
    metadata["contentType"] = m_contentTypeCombo->currentText();
    metadata["startTime"] = m_startTimeSpin->value();
    metadata["duration"] = m_durationSpin->value();
    metadata["muted"] = m_mutedCheck->isChecked();
    metadata["notes"] = m_notesEdit->toPlainText();

    emit regionUpdated(m_currentRegionId, metadata);
}

// TimelineWidget Implementation
TimelineWidget::TimelineWidget(QWidget *parent)
    : QWidget(parent)
{
    setupUI();
    setupLayout();
    setupConnections();
}

void TimelineWidget::setupUI()
{
    // Create main components
    m_toolbar = new TimelineToolbar(this);
    m_transportControls = new TransportControls(this);
    m_ruler = new TimelineRuler(this);
    m_metadataPanel = new RegionMetadataPanel(this);
    m_metadataPanel->setCompactMode(true);

    // Settings
    m_preserveTimingCheckBox = new QCheckBox("Preserve Original Timing");
    m_preserveTimingCheckBox->setChecked(true);
    m_preserveTimingCheckBox->setToolTip("Prevents regions from being moved in time when exported to DAW");
}

void TimelineWidget::setupLayout()
{
    setMinimumSize(1000, 600);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(4, 4, 4, 4);
    m_mainLayout->setSpacing(2);

    // Top toolbar
    m_mainLayout->addWidget(m_toolbar);

    // Transport controls with settings
    QWidget *transportWidget = new QWidget;
    QHBoxLayout *transportLayout = new QHBoxLayout(transportWidget);
    transportLayout->setContentsMargins(0, 0, 0, 0);
    transportLayout->addWidget(m_transportControls, 1);
    transportLayout->addWidget(m_preserveTimingCheckBox);
    m_mainLayout->addWidget(transportWidget);

    // Main timeline splitter
    m_timelineSplitter = new QSplitter(Qt::Horizontal);

    // Timeline area
    QWidget *timelineArea = new QWidget;
    QVBoxLayout *timelineLayout = new QVBoxLayout(timelineArea);
    timelineLayout->setContentsMargins(0, 0, 0, 0);
    timelineLayout->setSpacing(0);

    // Ruler
    timelineLayout->addWidget(m_ruler);

    // Scrollable timeline content with strict containment to prevent overflow
    m_timelineScrollArea = new QScrollArea;
    m_timelineScrollArea->setWidgetResizable(false);
    m_timelineScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_timelineScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_timelineScrollArea->setFrameStyle(QFrame::StyledPanel);

    // Critical: Set size policy to prevent expansion beyond container
    m_timelineScrollArea->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    m_timelineScrollArea->setMinimumHeight(200);  // Minimum height to prevent collapse
    m_timelineScrollArea->setMaximumHeight(QWIDGETSIZE_MAX);  // Allow expansion but within container

    // Enable proper viewport clipping and rendering with strict containment
    m_timelineScrollArea->setAttribute(Qt::WA_OpaquePaintEvent, true);
    m_timelineScrollArea->setAutoFillBackground(true);

    // Force clipping to prevent content from rendering outside scroll area bounds
    m_timelineScrollArea->setAttribute(Qt::WA_NoSystemBackground, false);

    // Configure viewport for proper clipping and prevent visual artifacts
    if (m_timelineScrollArea->viewport()) {
        m_timelineScrollArea->viewport()->setAttribute(Qt::WA_OpaquePaintEvent, true);
        m_timelineScrollArea->viewport()->setAutoFillBackground(true);
        m_timelineScrollArea->viewport()->setAttribute(Qt::WA_NoSystemBackground, false);

        // Critical: Force clipping on viewport to prevent overflow
        m_timelineScrollArea->viewport()->setAttribute(Qt::WA_PaintOnScreen, false);
        m_timelineScrollArea->viewport()->setAttribute(Qt::WA_StaticContents, false);
        m_timelineScrollArea->viewport()->setAttribute(Qt::WA_UpdatesDisabled, false);

        // Set background color to prevent flicker
        QPalette viewportPalette = m_timelineScrollArea->viewport()->palette();
        viewportPalette.setColor(QPalette::Window, QColor(30, 30, 30));
        m_timelineScrollArea->viewport()->setPalette(viewportPalette);
    }

    m_timelineContainer = new QWidget;
    m_timelineContainer->setAutoFillBackground(true);
    m_timelineContainer->setPalette(QPalette(QColor(30, 30, 30)));

    // Ensure proper clipping for timeline container with strict containment
    m_timelineContainer->setAttribute(Qt::WA_OpaquePaintEvent, true);
    m_timelineContainer->setAttribute(Qt::WA_NoSystemBackground, false);

    // Set size policy to prevent container from expanding beyond scroll area
    m_timelineContainer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    m_timelineLayout = new QVBoxLayout(m_timelineContainer);
    m_timelineLayout->setContentsMargins(0, 0, 0, 0);
    m_timelineLayout->setSpacing(0);

    // Ensure layout doesn't allow widgets to overflow
    m_timelineLayout->setSizeConstraint(QLayout::SetMinAndMaxSize);

    m_timelineScrollArea->setWidget(m_timelineContainer);

    // Add scroll area with proper containment - no stretch factor to prevent overflow
    timelineLayout->addWidget(m_timelineScrollArea, 1);

    // Add timeline area and metadata panel to splitter
    m_timelineSplitter->addWidget(timelineArea);
    m_timelineSplitter->addWidget(m_metadataPanel);
    m_timelineSplitter->setSizes({800, 250});

    m_mainLayout->addWidget(m_timelineSplitter, 1);
}

void TimelineWidget::setupConnections()
{
    // Ruler connections
    connect(m_ruler, &TimelineRuler::playheadMoved, this, &TimelineWidget::onPlayheadMoved);
    connect(m_ruler, &TimelineRuler::zoomRequested, this, &TimelineWidget::onZoomRequested);

    // Transport controls connections
    connect(m_transportControls, &TransportControls::playRequested, this, &TimelineWidget::onTransportPlayRequested);
    connect(m_transportControls, &TransportControls::pauseRequested, this, &TimelineWidget::onTransportPauseRequested);
    connect(m_transportControls, &TransportControls::stopRequested, this, &TimelineWidget::onTransportStopRequested);
    connect(m_transportControls, &TransportControls::positionChanged, this, &TimelineWidget::onTransportPositionChanged);
    connect(m_transportControls, &TransportControls::frameRateChanged, this, &TimelineWidget::onFrameRateChanged);
    connect(m_transportControls, &TransportControls::timecodeFormatChanged, this, &TimelineWidget::onTimecodeFormatChanged);

    // Toolbar connections
    connect(m_toolbar, &TimelineToolbar::zoomInRequested, this, &TimelineWidget::onToolbarZoomIn);
    connect(m_toolbar, &TimelineToolbar::zoomOutRequested, this, &TimelineWidget::onToolbarZoomOut);
    connect(m_toolbar, &TimelineToolbar::fitToWindowRequested, this, &TimelineWidget::onToolbarFitToWindow);
    connect(m_toolbar, &TimelineToolbar::showWaveformsToggled, this, &TimelineWidget::onShowWaveformsToggled);
    connect(m_toolbar, &TimelineToolbar::showRegionNamesToggled, this, &TimelineWidget::onShowRegionNamesToggled);
    connect(m_toolbar, &TimelineToolbar::showConfidenceToggled, this, &TimelineWidget::onShowConfidenceToggled);
    connect(m_toolbar, &TimelineToolbar::trackSelectionRequested, this, &TimelineWidget::onToolbarTrackSelection);

    // Metadata panel connections
    connect(m_metadataPanel, &RegionMetadataPanel::regionUpdated, this, &TimelineWidget::regionUpdated);

    // Scroll synchronization - connect after scroll area is created
    QTimer::singleShot(0, this, [this]() {
        if (m_timelineScrollArea && m_timelineScrollArea->horizontalScrollBar()) {
            connect(m_timelineScrollArea->horizontalScrollBar(), &QScrollBar::valueChanged,
                    this, &TimelineWidget::onHorizontalScrollChanged);
        }
    });
}

void TimelineWidget::setAAFReader(AAFReader *reader)
{
    m_aafReader = reader;
}

void TimelineWidget::setAudioFileManager(AudioFileManager *manager)
{
    // Simply store the manager - avoid any complex operations during startup
    m_audioFileManager = manager;

    // Audio playback manager integration will be handled later when needed
    // This prevents startup crashes while preserving functionality
}

bool TimelineWidget::preserveOriginalTiming() const
{
    return m_preserveTimingCheckBox ? m_preserveTimingCheckBox->isChecked() : true;
}

void TimelineWidget::setFrameRate(double frameRate)
{
    m_frameRate = frameRate;
    m_ruler->setFrameRate(frameRate);
    m_transportControls->setFrameRate(frameRate);
}

void TimelineWidget::setTimecodeFormat(int format)
{
    m_timecodeFormat = format;
    m_ruler->setTimecodeFormat(format);
    m_transportControls->setTimecodeFormat(format);
}

void TimelineWidget::refreshTimeline()
{
    updateTrackDisplay();
    updateTimelineSize();
    update();
}

void TimelineWidget::forceRefresh()
{
    qDebug() << "TimelineWidget::forceRefresh: Forcing complete timeline refresh";

    // Force immediate update of all components
    updateTrackDisplay();
    updateTimelineSize();

    // Force repaint of all widgets
    if (m_ruler) {
        m_ruler->update();
        m_ruler->repaint();
    }

    for (auto content : m_trackContents) {
        content->update();
        content->repaint();
    }

    for (auto header : m_trackHeaders) {
        header->update();
        header->repaint();
    }

    // Force scroll area update
    if (m_timelineScrollArea) {
        m_timelineScrollArea->update();
        m_timelineScrollArea->repaint();
        if (m_timelineScrollArea->viewport()) {
            m_timelineScrollArea->viewport()->update();
            m_timelineScrollArea->viewport()->repaint();
        }
    }

    update();
    repaint();

    qDebug() << "TimelineWidget::forceRefresh: Refresh completed";
}

void TimelineWidget::setAudioPlaybackManager(AudioPlaybackManager *manager)
{
    m_audioPlaybackManager = manager;
    if (m_audioPlaybackManager) {
        // Deferred initialization to prevent startup issues
        QTimer::singleShot(100, this, &TimelineWidget::initializeAudioComponents);
    }
}

void TimelineWidget::loadTimelineData(const QVariantList &regions, const QVariantList &tracks)
{
    // Clear existing data
    m_tracks.clear();
    m_regions.clear();

    // Clear existing UI
    for (auto header : m_trackHeaders) {
        header->deleteLater();
    }
    for (auto content : m_trackContents) {
        content->deleteLater();
    }
    m_trackHeaders.clear();
    m_trackContents.clear();

    // Create tracks from regions if no tracks provided
    if (tracks.isEmpty()) {
        createTracksFromRegions(regions);
    } else {
        // Load provided tracks
        for (const auto &trackData : tracks) {
            QVariantMap trackMap = trackData.toMap();
            TimelineTrack track;
            track.name = trackMap.value("name").toString();
            track.type = trackMap.value("type").toString();
            track.visible = trackMap.value("visible", true).toBool();
            track.muted = trackMap.value("muted", false).toBool();
            track.solo = trackMap.value("solo", false).toBool();
            track.color = getColorForContentType(track.type);
            m_tracks.append(track);
        }
    }

    // Load regions
    QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
    bool mockDisabled = (disableMock == "1" || disableMock.toLower() == "true");

    for (const auto &regionData : regions) {
        QVariantMap regionMap = regionData.toMap();

        TimelineRegion region;
        region.id = regionMap.value("regionId", regionMap.value("id")).toString();

        // Extract both name sources for toggle functionality
        QString sourceAName = regionMap.value("name").toString();  // Source A: AAF clip names
        QString sourceBName;  // Source B: Descriptions/metadata
        QString audioFilePath = regionMap.value("audioFilePath", regionMap.value("essenceFilePath")).toString();

        // Source A: File-based names (UnnamedA01.66EAEC966EAEBE1A format)
        if (sourceAName.isEmpty() || sourceAName == "Clip" || sourceAName.startsWith("region_")) {
            if (!audioFilePath.isEmpty()) {
                QFileInfo fileInfo(audioFilePath);
                sourceAName = fileInfo.baseName(); // Use filename without extension
            }
        }

        // Source B: Try to extract descriptive names from metadata or essence file names
        QVariantMap metadata = regionMap.value("metadata").toMap();
        sourceBName = metadata.value("description").toString();
        if (sourceBName.isEmpty()) {
            sourceBName = metadata.value("comment").toString();
        }
        if (sourceBName.isEmpty()) {
            // Try essence file name as alternative
            QString essenceFileName = regionMap.value("essenceFileName").toString();
            if (!essenceFileName.isEmpty() && essenceFileName != sourceAName) {
                sourceBName = essenceFileName;
            }
        }

        // Fallback names
        if (sourceAName.isEmpty()) {
            sourceAName = region.id;
        }
        if (sourceBName.isEmpty()) {
            sourceBName = sourceAName;  // Use Source A as fallback
        }

        region.name = sourceAName;
        region.alternativeName = sourceBName;
        region.trackName = regionMap.value("trackName", regionMap.value("track")).toString();

        // Extract enhanced stereo channel information from LibAAF
        region.channelNumber = regionMap.value("channelNumber", 0).toInt();
        region.stereoGroupId = regionMap.value("stereoGroupId", QString()).toString();

        // Get channel type information
        QString channelType = regionMap.value("channelType", QString()).toString();

        // If LibAAF didn't provide stereo info, fall back to track-based detection
        int trackNumber = regionMap.value("trackNumber", -1).toInt();
        if (region.stereoGroupId.isEmpty() && trackNumber > 0) {
            // Group consecutive tracks as stereo pairs (1-2, 3-4, 5-6, etc.)
            int stereoGroupNumber = (trackNumber - 1) / 2 + 1;
            region.stereoGroupId = QString("StereoGroup_%1").arg(stereoGroupNumber);

            // Assign channel number if not already set
            if (region.channelNumber == 0) {
                region.channelNumber = ((trackNumber - 1) % 2) + 1;  // 1 for left, 2 for right
            }
        }

        // Store additional stereo information in metadata for debugging
        if (!channelType.isEmpty()) {
            region.metadata["channelType"] = channelType;
        }
        region.metadata["originalTrackNumber"] = trackNumber;

        // Skip mock regions when mock data is disabled
        if (mockDisabled && (region.id.contains("❌") || region.id.contains("MOCK_") || region.id.contains("FAKE_") ||
                            region.name.contains("❌") || region.name.contains("MOCK_") || region.name.contains("FAKE_") ||
                            region.trackName.contains("❌") || region.trackName.contains("MOCK_") || region.trackName.contains("FAKE_"))) {
            continue;
        }

        // Use correct field names from LibAAF
        region.startTime = regionMap.value("position", regionMap.value("startTime", 0.0)).toDouble();
        region.duration = regionMap.value("length", regionMap.value("duration", 1.0)).toDouble();
        region.contentType = regionMap.value("contentType", "Unknown").toString();
        region.speakerId = regionMap.value("speakerId").toString();
        region.confidence = regionMap.value("confidence", 0.0).toDouble();
        region.audioFilePath = regionMap.value("essenceFilePath", regionMap.value("audioFilePath")).toString();
        region.color = getColorForContentType(region.contentType);
        region.metadata = regionMap;

        m_regions[region.id] = region;

        // Add region to appropriate track
        bool regionAssigned = false;
        for (auto &track : m_tracks) {
            // First priority: exact original track name match (from AAF)
            if (!region.trackName.isEmpty() && track.originalName == region.trackName) {
                track.regions.append(region);
                regionAssigned = true;
                break;
            }
            // Second priority: track number match (from LibAAF trackNumber field)
            else if (track.trackNumber >= 0 && regionMap.contains("trackNumber") &&
                     track.trackNumber == regionMap.value("trackNumber").toInt()) {
                track.regions.append(region);
                regionAssigned = true;
                break;
            }
            // Third priority: content type match
            else if (track.type == region.contentType && region.contentType != "Unknown") {
                track.regions.append(region);
                regionAssigned = true;
                break;
            }
        }

        // If no matching track found, assign to first track
        if (!regionAssigned && !m_tracks.isEmpty()) {
            m_tracks.first().regions.append(region);
        }
    }

    // Calculate duration
    m_duration = 0.0;
    for (const auto &region : m_regions) {
        m_duration = qMax(m_duration, region.startTime + region.duration);
    }
    m_duration = qMax(m_duration, 300.0); // Minimum 5 minutes

    // Update UI components
    m_ruler->setDuration(m_duration);
    m_transportControls->setDuration(m_duration);

    // Update track display
    updateTrackDisplay();
    updateTimelineSize();

    qDebug() << "TimelineWidget::loadTimelineData: Loaded" << m_tracks.size() << "tracks," << m_regions.size() << "regions, duration:" << m_duration;

    // Force scroll area update
    if (m_timelineScrollArea) {
        m_timelineScrollArea->setVisible(true);
        m_timelineScrollArea->update();
        m_timelineScrollArea->repaint();
    }

    // Update all track content widgets
    for (auto content : m_trackContents) {
        if (content) {
            content->setVisible(true);
            content->update();
            content->repaint();
        }
    }

    // Force this widget to update
    setVisible(true);
    update();
    repaint();

    qDebug() << "TimelineWidget::loadTimelineData: UI update completed";
}

void TimelineWidget::createTracksFromRegions(const QVariantList &regions)
{
    // Use QMap to preserve track order by trackIndex from AAF
    QMap<int, QString> orderedTrackNames;
    QSet<QString> contentTypes;
    QSet<QString> speakers;

    // First, collect track names with their AAF order indices
    for (const auto &regionData : regions) {
        QVariantMap regionMap = regionData.toMap();
        // Use correct field names from LibAAF
        QString trackName = regionMap.value("trackName", regionMap.value("track")).toString();
        int trackIndex = regionMap.value("trackNumber", regionMap.value("trackIndex", -1)).toInt();
        QString contentType = regionMap.value("contentType", "Unknown").toString();
        QString speakerId = regionMap.value("speakerId").toString();

        // Collect actual track names from AAF with their original order
        if (!trackName.isEmpty()) {
            if (trackIndex >= 0) {
                // Use AAF track index to preserve order
                orderedTrackNames[trackIndex] = trackName;
            } else {
                // Fallback: assign a high index if trackIndex is missing
                int fallbackIndex = 1000 + orderedTrackNames.size();
                orderedTrackNames[fallbackIndex] = trackName;
            }
        }

        // Also collect content types for classification-based tracks
        contentTypes.insert(contentType);
        if (!speakerId.isEmpty() && contentType == "Speech") {
            speakers.insert(speakerId);
        }
    }

    // Create tracks based on AAF track names in their original order (preferred)
    if (!orderedTrackNames.isEmpty()) {
        // QMap automatically sorts by key (trackIndex), preserving AAF order
        int displayTrackNumber = 1;
        for (auto it = orderedTrackNames.begin(); it != orderedTrackNames.end(); ++it) {
            const QString &originalTrackName = it.value();

            // Skip mock track names when mock data is disabled
            QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
            if ((disableMock == "1" || disableMock.toLower() == "true") &&
                (originalTrackName.contains("❌") || originalTrackName.contains("MOCK_") || originalTrackName.contains("FAKE_"))) {
                continue;
            }

            TimelineTrack track;
            // Add track numbering: "1. Track Name"
            track.name = QString("%1. %2").arg(displayTrackNumber).arg(originalTrackName);
            track.originalName = originalTrackName; // Store original name for matching
            track.trackNumber = it.key(); // Store AAF track number
            track.type = "Audio"; // Default type for AAF tracks
            track.color = getColorForContentType("Audio");
            m_tracks.append(track);

            displayTrackNumber++;
        }
    }

    // If no valid tracks from AAF, fall back to content-type based tracks
    if (m_tracks.isEmpty() && !contentTypes.isEmpty()) {
        for (const QString &contentType : contentTypes) {
            if (contentType == "Speech") {
                // Create separate tracks for each speaker
                for (const QString &speaker : speakers) {
                    TimelineTrack track;
                    track.name = QString("Speech_%1").arg(speaker);
                    track.type = "Speech";
                    track.color = getColorForContentType("Speech");
                    m_tracks.append(track);
                }
            } else if (contentType != "Unknown") {
                TimelineTrack track;
                track.name = contentType;
                track.type = contentType;
                track.color = getColorForContentType(contentType);
                m_tracks.append(track);
            }
        }
    }

    // If still no tracks, create a default track
    if (m_tracks.isEmpty()) {
        TimelineTrack track;
        track.name = "Audio Track 1";
        track.type = "Audio";
        track.color = getColorForContentType("Audio");
        m_tracks.append(track);
    }
}

QColor TimelineWidget::getColorForContentType(const QString &contentType) const
{
    if (contentType == "Speech") return QColor(100, 150, 255);
    if (contentType == "Music") return QColor(255, 150, 100);
    if (contentType == "SFX") return QColor(150, 255, 100);
    if (contentType == "Ambience") return QColor(200, 200, 100);
    if (contentType == "Silence") return QColor(100, 100, 100);
    return QColor(150, 150, 150); // Unknown
}

// Removed - replaced by updateTrackDisplay()

// Removed - replaced by updateTrackDisplay()

void TimelineWidget::updateRegionClassification(const QString &regionId, const QVariantMap &classification)
{
    if (m_regions.contains(regionId)) {
        TimelineRegion &region = m_regions[regionId];
        region.contentType = classification.value("contentType").toString();
        region.speakerId = classification.value("speakerId").toString();
        region.confidence = classification.value("confidence").toDouble();
        region.color = getColorForContentType(region.contentType);

        // Update the region in tracks
        for (auto &track : m_tracks) {
            for (auto &trackRegion : track.regions) {
                if (trackRegion.id == regionId) {
                    trackRegion = region;
                    break;
                }
            }
        }

        // Update UI
        for (auto content : m_trackContents) {
            content->update();
        }

        // Update metadata panel if this region is selected
        if (m_selectedRegionId == regionId) {
            m_metadataPanel->setRegion(region);
        }
    }
}

void TimelineWidget::zoomIn()
{
    m_zoom = qMin(m_zoom * 1.5, 10.0);
    updateZoom();
}

void TimelineWidget::zoomOut()
{
    m_zoom = qMax(m_zoom / 1.5, 0.1);
    updateZoom();
}

void TimelineWidget::fitToWindow()
{
    if (m_duration > 0) {
        int availableWidth = m_timelineScrollArea->viewport()->width() - 50;
        m_zoom = availableWidth / (m_duration * 100.0);
        m_zoom = qMax(m_zoom, 0.1);
        updateZoom();
    }
}

void TimelineWidget::updateZoom()
{
    // Calculate new width with bounds checking
    int newWidth = static_cast<int>(m_duration * 100.0 * m_zoom);
    newWidth = qMax(newWidth, 100); // Minimum width
    newWidth = qMin(newWidth, 100000); // Maximum width to prevent memory issues

    // Update ruler with proper size constraints
    if (m_ruler) {
        m_ruler->setZoom(m_zoom);
        m_ruler->setFixedWidth(newWidth);
        m_ruler->updateGeometry();
    }

    // Update all track contents with proper containment
    for (auto content : m_trackContents) {
        content->setZoom(m_zoom);

        // Set fixed width to prevent overflow
        content->setFixedWidth(newWidth);
        content->updateGeometry();

        // Use update() instead of repaint() for better performance
        content->update();
    }

    // Update timeline container size with proper constraints
    if (m_timelineContainer) {
        m_timelineContainer->setFixedWidth(newWidth);
        m_timelineContainer->updateGeometry();

        // Ensure container stays within scroll area bounds
        QSize containerSize = m_timelineContainer->sizeHint();
        m_timelineContainer->resize(containerSize);
    }

    // Update scroll area to handle new content size
    if (m_timelineScrollArea) {
        m_timelineScrollArea->updateGeometry();
        m_timelineScrollArea->update();

        // Ensure scroll area viewport is properly updated
        if (m_timelineScrollArea->viewport()) {
            m_timelineScrollArea->viewport()->update();
        }
    }
}

void TimelineWidget::playPause()
{
    if (!m_audioPlaybackManager) {
        return;
    }

    if (m_audioPlaybackManager->isPlaying()) {
        m_audioPlaybackManager->pause();
    } else {
        m_audioPlaybackManager->play();
    }
}

void TimelineWidget::stop()
{
    if (m_audioPlaybackManager) {
        m_audioPlaybackManager->stop();
    }

    m_transportControls->stop();
    m_playheadPosition = 0.0;
    onPlayheadMoved(0.0);
}

void TimelineWidget::onPlayheadMoved(double position)
{
    m_playheadPosition = position;
    m_ruler->setPlayheadPosition(position);
    m_transportControls->setPlayheadPosition(position);

    for (auto content : m_trackContents) {
        content->setPlayheadPosition(position);
    }
}

void TimelineWidget::onRegionSelected(const QString &regionId)
{
    m_selectedRegionId = regionId;

    // Update selection in all tracks
    for (auto &track : m_tracks) {
        for (auto &region : track.regions) {
            region.selected = (region.id == regionId);
        }
    }

    // Update UI
    for (auto content : m_trackContents) {
        content->update();
    }

    // Update metadata panel
    if (m_regions.contains(regionId)) {
        m_metadataPanel->setRegion(m_regions[regionId]);

        if (m_audioPlaybackManager) {
            TimelineRegion region = m_regions[regionId];
            // Set playhead to region start
            m_audioPlaybackManager->setPosition(region.startTime);
        }
    }

    emit regionSelected(regionId);
}

void TimelineWidget::onTrackMuteToggled(const QString &trackName, bool muted)
{
    for (auto &track : m_tracks) {
        if (track.name == trackName) {
            track.muted = muted;
            break;
        }
    }

    // Update track contents
    for (auto content : m_trackContents) {
        content->update();
    }
}

void TimelineWidget::onTrackSoloToggled(const QString &trackName, bool solo)
{
    for (auto &track : m_tracks) {
        if (track.name == trackName) {
            track.solo = solo;
        } else if (solo) {
            track.solo = false; // Only one track can be soloed
        }
    }

    // Update track headers and contents
    for (int i = 0; i < m_tracks.size(); ++i) {
        if (i < m_trackHeaders.size()) {
            m_trackHeaders[i]->updateTrack(m_tracks[i]);
        }
        if (i < m_trackContents.size()) {
            m_trackContents[i]->update();
        }
    }
}

void TimelineWidget::onTrackSelectionToggled(const QString &trackName, bool selected)
{
    // Update track selection state
    for (auto &track : m_tracks) {
        if (track.name == trackName) {
            track.selected = selected;
            break;
        }
    }

    // Track selection is now handled directly in track headers

    qDebug() << "Track" << trackName << "selection changed to:" << selected;
}

void TimelineWidget::onRegionContextMenu(const QString &regionId, const QPoint &position)
{
    QMenu contextMenu;

    QAction *playAction = contextMenu.addAction("Play Region");
    QAction *editAction = contextMenu.addAction("Edit Metadata");
    contextMenu.addSeparator();
    QAction *deleteAction = contextMenu.addAction("Delete Region");

    QAction *selectedAction = contextMenu.exec(position);

    if (selectedAction == playAction) {
        // Play the selected region
        if (m_audioPlaybackManager && m_regions.contains(regionId)) {
            TimelineRegion region = m_regions[regionId];

            QVariantMap regionInfo;
            regionInfo["id"] = region.id;
            regionInfo["name"] = region.name;
            regionInfo["startTime"] = region.startTime;
            regionInfo["duration"] = region.duration;
            regionInfo["audioFilePath"] = region.audioFilePath;

            m_audioPlaybackManager->playRegion(regionInfo);
        }
        emit playbackRequested(regionId);
    } else if (selectedAction == editAction) {
        onRegionSelected(regionId);
    } else if (selectedAction == deleteAction) {
        // Handle region deletion
        // This would need to be implemented based on the application's needs
    }
}





// Track Selection Implementation





QStringList TimelineWidget::getSelectedTrackNames() const
{
    QStringList selectedTracks;
    for (const auto &track : m_tracks) {
        if (track.selected) {
            selectedTracks.append(track.name);
        }
    }
    return selectedTracks;
}

QStringList TimelineWidget::getSelectedRegionIds() const
{
    QStringList selectedRegions;
    for (const auto &region : m_regions) {
        if (region.selected) {
            selectedRegions.append(region.id);
        }
    }
    return selectedRegions;
}

QVariantList TimelineWidget::getSelectedRegions() const
{
    QVariantList selectedRegions;

    for (const auto &track : m_tracks) {
        if (!track.selected) continue; // Skip unselected tracks

        for (const auto &region : track.regions) {
            if (region.selected) {
                QVariantMap regionData;
                regionData["id"] = region.id;
                regionData["name"] = region.name;
                regionData["trackName"] = region.trackName;
                regionData["startTime"] = region.startTime;
                regionData["duration"] = region.duration;
                regionData["contentType"] = region.contentType;
                regionData["speakerId"] = region.speakerId;
                regionData["confidence"] = region.confidence;
                regionData["audioFilePath"] = region.audioFilePath;
                regionData["metadata"] = region.metadata;
                selectedRegions.append(regionData);
            }
        }
    }

    return selectedRegions;
}

void TimelineWidget::setTrackSelection(const QString &trackName, bool selected)
{
    for (int i = 0; i < m_tracks.size(); ++i) {
        if (m_tracks[i].name == trackName) {
            m_tracks[i].selected = selected;
            // Update corresponding track header
            if (i < m_trackHeaders.size()) {
                m_trackHeaders[i]->setSelected(selected);
            }
            break;
        }
    }
    syncTrackSelection();
}

void TimelineWidget::setAllTracksSelected(bool selected)
{
    for (auto &track : m_tracks) {
        track.selected = selected;
    }
    for (auto header : m_trackHeaders) {
        header->setSelected(selected);
    }
    syncTrackSelection();
}





void TimelineWidget::setTimeRangeHighlight(double startTime, double endTime, bool enabled)
{
    m_timeRangeHighlightEnabled = enabled;
    m_timeRangeStartTime = startTime;
    m_timeRangeEndTime = endTime;

    // Update all track content widgets to show the highlight
    for (auto trackContent : m_trackContents) {
        trackContent->update(); // Trigger repaint
    }

    qDebug() << "Time range highlight set:" << enabled << "from" << startTime << "to" << endTime;
}

void TimelineWidget::clearTimeRangeHighlight()
{
    setTimeRangeHighlight(0.0, 0.0, false);
}

// New Timeline Widget Methods

void TimelineWidget::updateTrackDisplay()
{
    // Clear existing track widgets
    for (auto header : m_trackHeaders) {
        header->deleteLater();
    }
    for (auto content : m_trackContents) {
        content->deleteLater();
    }
    m_trackHeaders.clear();
    m_trackContents.clear();

    // Clear timeline layout
    QLayoutItem *item;
    while ((item = m_timelineLayout->takeAt(0)) != nullptr) {
        delete item->widget();
        delete item;
    }

    // Create tracks widget with headers and content
    m_tracksWidget = new QWidget;
    QHBoxLayout *tracksLayout = new QHBoxLayout(m_tracksWidget);
    tracksLayout->setContentsMargins(0, 0, 0, 0);
    tracksLayout->setSpacing(0);

    // Headers column
    m_trackHeadersWidget = new QWidget;
    m_trackHeadersWidget->setFixedWidth(HEADER_WIDTH);
    m_trackHeadersLayout = new QVBoxLayout(m_trackHeadersWidget);
    m_trackHeadersLayout->setContentsMargins(0, 0, 0, 0);
    m_trackHeadersLayout->setSpacing(0);

    // Contents column
    m_trackContentsWidget = new QWidget;
    m_trackContentsLayout = new QVBoxLayout(m_trackContentsWidget);
    m_trackContentsLayout->setContentsMargins(0, 0, 0, 0);
    m_trackContentsLayout->setSpacing(0);

    // Create track widgets
    for (int i = 0; i < m_tracks.size(); ++i) {
        // Create header
        TrackHeader *header = new TrackHeader(m_tracks[i]);
        m_trackHeaders.append(header);
        m_trackHeadersLayout->addWidget(header);

        // Connect header signals
        connect(header, &TrackHeader::selectionToggled, this, &TimelineWidget::onTrackSelectionToggled);
        connect(header, &TrackHeader::muteToggled, this, &TimelineWidget::onTrackMuteToggled);
        connect(header, &TrackHeader::soloToggled, this, &TimelineWidget::onTrackSoloToggled);

        // Create content
        TrackContent *content = new TrackContent(m_tracks[i]);
        content->setZoom(m_zoom);
        content->setDuration(m_duration);
        content->setPlayheadPosition(m_playheadPosition);
        content->setTimelineWidget(this);
        content->setTimeRangeHighlight(m_timeRangeStartTime, m_timeRangeEndTime, m_timeRangeHighlightEnabled);
        m_trackContents.append(content);
        m_trackContentsLayout->addWidget(content);

        // Connect content signals
        connect(content, &TrackContent::regionSelected, this, &TimelineWidget::onRegionSelected);
        connect(content, &TrackContent::zoomRequested, this, &TimelineWidget::onZoomRequested);
    }

    tracksLayout->addWidget(m_trackHeadersWidget);
    tracksLayout->addWidget(m_trackContentsWidget, 1);

    m_timelineLayout->addWidget(m_tracksWidget);
}

void TimelineWidget::updateTimelineSize()
{
    if (m_trackContentsWidget && m_trackHeadersWidget) {
        int totalHeight = m_tracks.size() * TRACK_HEIGHT;
        m_trackContentsWidget->setMinimumHeight(totalHeight);
        m_trackContentsWidget->setFixedHeight(totalHeight);
        m_trackHeadersWidget->setMinimumHeight(totalHeight);
        m_trackHeadersWidget->setFixedHeight(totalHeight);

        int timelineWidth = qMax(1000, static_cast<int>(m_duration * 100.0 * m_zoom));
        m_trackContentsWidget->setMinimumWidth(timelineWidth);
        m_trackContentsWidget->setFixedWidth(timelineWidth);

        // Update timeline container size
        if (m_timelineContainer) {
            m_timelineContainer->setMinimumSize(timelineWidth, totalHeight);
            m_timelineContainer->setFixedSize(timelineWidth, totalHeight);
            m_timelineContainer->updateGeometry();
        }
    }
}

void TimelineWidget::syncTrackSelection()
{
    QStringList selectedTracks = getSelectedTrackNames();
    emit trackSelectionChanged(selectedTracks);
}

void TimelineWidget::initializeAudioComponents()
{
    if (m_audioPlaybackManager && m_audioFileManager) {
        m_audioPlaybackManager->setAudioFileManager(m_audioFileManager);

        // Connect audio signals
        connect(m_audioPlaybackManager, &AudioPlaybackManager::positionChanged,
                this, &TimelineWidget::onAudioPlaybackPositionChanged);
        connect(m_audioPlaybackManager, &AudioPlaybackManager::durationChanged,
                this, &TimelineWidget::onAudioPlaybackDurationChanged);
        connect(m_audioPlaybackManager, &AudioPlaybackManager::isPlayingChanged,
                this, &TimelineWidget::onAudioPlaybackStateChanged);
    }
}

// Slot Implementations

void TimelineWidget::onZoomRequested(double factor)
{
    double newZoom = m_zoom * factor;
    newZoom = qMax(0.1, qMin(10.0, newZoom));

    if (qAbs(newZoom - m_zoom) > 0.01) {
        m_zoom = newZoom;
        updateZoom();
    }
}

void TimelineWidget::onTransportPlayRequested()
{
    emit playbackRequested("");
    m_transportControls->play();
}

void TimelineWidget::onTransportPauseRequested()
{
    m_transportControls->pause();
}

void TimelineWidget::onTransportStopRequested()
{
    m_transportControls->stop();
    onPlayheadMoved(0.0);
}

void TimelineWidget::onTransportPositionChanged(double position)
{
    onPlayheadMoved(position);
}

void TimelineWidget::onFrameRateChanged(double frameRate)
{
    setFrameRate(frameRate);
}

void TimelineWidget::onTimecodeFormatChanged(int format)
{
    setTimecodeFormat(format);
}

void TimelineWidget::onToolbarZoomIn()
{
    zoomIn();
}

void TimelineWidget::onToolbarZoomOut()
{
    zoomOut();
}

void TimelineWidget::onToolbarFitToWindow()
{
    fitToWindow();
}

void TimelineWidget::onToolbarTrackSelection(bool selectAll)
{
    setAllTracksSelected(selectAll);
}

void TimelineWidget::onShowWaveformsToggled(bool show)
{
    m_showWaveforms = show;
    for (auto content : m_trackContents) {
        content->update();
    }
}

void TimelineWidget::onShowRegionNamesToggled(bool show)
{
    m_showRegionNames = show;
    for (auto content : m_trackContents) {
        content->update();
    }
}

void TimelineWidget::onToggleRegionNameSource()
{
    m_useAlternativeRegionNames = !m_useAlternativeRegionNames;

    // Update the tooltip to show current source
    if (m_toolbar && m_toolbar->getToggleRegionNameSourceAction()) {
        QString tooltip = m_useAlternativeRegionNames ?
            "Region Names: Source B (Descriptions)" :
            "Region Names: Source A (File Names)";
        m_toolbar->getToggleRegionNameSourceAction()->setToolTip(tooltip);
        m_toolbar->getToggleRegionNameSourceAction()->setChecked(m_useAlternativeRegionNames);
    }

    // Force refresh of all track contents to show new names
    for (auto content : m_trackContents) {
        content->update();
    }

    qDebug() << "TimelineWidget: Toggled region name source to"
             << (m_useAlternativeRegionNames ? "Source B (Descriptions)" : "Source A (File Names)");
}

void TimelineWidget::onShowConfidenceToggled(bool show)
{
    m_showConfidence = show;
    for (auto content : m_trackContents) {
        content->update();
    }
}

void TimelineWidget::onAudioPlaybackPositionChanged(double position)
{
    onPlayheadMoved(position);
}

void TimelineWidget::onAudioPlaybackDurationChanged(double duration)
{
    if (duration > 0) {
        m_duration = qMax(m_duration, duration);
        m_ruler->setDuration(m_duration);
        m_transportControls->setDuration(m_duration);
        updateTimelineSize();
    }
}

void TimelineWidget::onAudioPlaybackStateChanged(bool isPlaying)
{
    if (isPlaying) {
        m_transportControls->play();
    } else {
        m_transportControls->pause();
    }
}

void TimelineWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // Update timeline size when widget is resized
    updateTimelineSize();

    // Ensure proper redraw of all components
    if (m_ruler) {
        m_ruler->update();
    }

    for (auto content : m_trackContents) {
        content->update();
    }

    for (auto header : m_trackHeaders) {
        header->update();
    }
}

void TimelineWidget::onHorizontalScrollChanged(int value)
{
    // Synchronize ruler position with timeline scroll
    if (m_ruler && m_timelineScrollArea) {
        // Calculate the scroll offset
        QScrollBar *hScrollBar = m_timelineScrollArea->horizontalScrollBar();
        if (hScrollBar) {
            int maxScroll = hScrollBar->maximum();
            int rulerWidth = m_ruler->width();
            int viewportWidth = m_timelineScrollArea->viewport()->width();

            if (maxScroll > 0 && rulerWidth > viewportWidth) {
                // Calculate proportional offset for ruler
                double scrollRatio = static_cast<double>(value) / maxScroll;
                int rulerOffset = static_cast<int>(scrollRatio * (rulerWidth - viewportWidth));

                // Apply offset to ruler (this would need a custom ruler widget with scroll support)
                // For now, just trigger an update
                m_ruler->update();
            }
        }
    }
}
